# MT5 EA 风险控制升级总结

## 概述
本次升级为 `gaopin2.mql5` EA 集成了三个关键的风险管理与灵活性增强功能，旨在防止策略在强单边趋势行情中因逆势网格马丁加仓而爆仓。

## 升级功能详细说明

### 1. 大周期趋势过滤器 (HTF Trend Filter)

**功能目标**: 建立"顶层规则"，禁止EA逆着市场主要趋势进行开仓或加仓操作。

**新增参数**:
```mql5
input bool   InpEnableTrendFilter = true;           // 启用/禁用趋势过滤器
input ENUM_TIMEFRAMES InpMATimeframe = PERIOD_H1;   // MA时间周期
input int    InpMAPeriod = 200;                     // MA计算周期
input ENUM_MA_METHOD InpMAMethod = MODE_EMA;        // MA计算方法
```

**核心实现**:
- 在 `OnInit()` 中创建MA指标句柄
- 新增 `CheckTrendFilter()` 函数检查交易方向是否符合趋势
- 在 `OpenInitialPositions()` 和 `AddPosition()` 中集成趋势检查
- **规则**: 价格低于MA禁止买入，价格高于MA禁止卖出

### 2. 网格层数硬止损 (Grid Level Hard Stop)

**功能目标**: 设置绝对的加仓上限，达到后立即无条件止损。

**新增参数**:
```mql5
input bool   InpEnableGridLevelStop = true;         // 启用最大加仓层数止损
input int    InpMaxGridLevel = 7;                   // 允许的最大加仓次数
```

**核心实现**:
- 在 `OnTick()` 函数开始处检查 `AdditionCount >= InpMaxGridLevel`
- 条件满足时立即调用 `CloseAllPositions()` 执行熔断止损
- 提供明确的日志说明止损原因

### 3. 灵活手数管理模式 (Flexible Lot Sizing Mode)

**功能目标**: 提供更平缓的手数递增选项，降低马丁策略的指数级风险。

**新增参数**:
```mql5
input bool   InpUseLotMultiplier = true;            // 选择手数管理模式
input double InpLotMultiplier = 1.4;                // 手数递增乘数
input double InpMaxLot = 5.0;                       // 单笔订单最大手数上限
```

**核心实现**:
- 在 `AddPosition()` 中根据 `InpUseLotMultiplier` 选择计算模式
- **新模式**: 获取最后一笔订单手数 × 乘数，受最大手数限制
- **旧模式**: 保持原有的固定手数数组逻辑
- 新增 `GetLastPositionLotSize()` 辅助函数

## 代码修改位置

### 参数区域 (第53-81行)
- 重新组织参数结构，添加分组标题
- 新增三组风险控制参数

### 全局变量 (第201行)
- 添加 `trendMAHandle` 用于趋势过滤器

### 初始化函数 `OnInit()` (第317-333行)
- 初始化趋势过滤器MA指标句柄
- 添加错误检查和日志输出

### 清理函数 `OnDeinit()` (第370-386行)
- 释放趋势过滤器MA指标句柄

### 主循环 `OnTick()` (第388-405行)
- 在函数开始处添加网格层数硬止损检查

### 新增函数
1. **`CheckTrendFilter()`** (第1057-1096行): 趋势过滤器检查
2. **`GetLastPositionLotSize()`** (第1099-1122行): 获取最后持仓手数

### 修改的核心函数
1. **`OpenInitialPositions()`**: 集成趋势过滤器检查
2. **`AddPosition()`**: 集成趋势过滤器和灵活手数管理

## 风险控制逻辑

### 执行优先级
1. **最高优先级**: 网格层数硬止损 (在OnTick开始处检查)
2. **中等优先级**: 趋势过滤器 (在开仓/加仓前检查)
3. **基础优先级**: 手数管理 (在确定交易手数时应用)

### 安全机制
- 所有新功能都有对应的开关参数
- 趋势过滤器失败时默认允许交易，避免阻塞
- 保持原有代码结构和核心交易逻辑不变

## 使用建议

### 推荐设置
- **趋势过滤器**: 启用，使用H1周期200期EMA
- **网格层数**: 设置为5-10层，根据风险承受能力调整
- **手数乘数**: 1.2-1.5之间，避免过于激进

### 注意事项
- 首次使用建议在模拟账户测试
- 根据不同品种调整参数
- 定期监控EA运行日志
- 建议配合其他风险管理工具使用

## 兼容性
- 完全兼容原有EA的所有功能
- 可以选择性启用/禁用新功能
- 保持原有参数和交易逻辑不变
