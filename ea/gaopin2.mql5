#property copyright "Copyright 2023, Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.30"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Arrays\ArrayObj.mqh>

enum ENUM_TREND_TYPE
{
   TREND_UP = 1,
   TREND_DOWN = 2,
   TREND_SIDEWAYS = 3
};

enum ENUM_TRADE_DIRECTION
{
   TRADE_DIRECTION_BUY = 1,
   TRADE_DIRECTION_SELL = 2,
   TRADE_DIRECTION_BOTH = 3,
   TRADE_DIRECTION_PREVIOUS = 4,
   TRADE_DIRECTION_MFI = 5
};

enum ENUM_SECOND_TIMEFRAME
{
   SECOND_1 = 1,
   SECOND_2 = 2,
   SECOND_3 = 3,
   SECOND_4 = 4,
   SECOND_5 = 5,
   SECOND_6 = 6,
   SECOND_7 = 7,
   SECOND_8 = 8,
   SECOND_9 = 9,
   SECOND_10 = 10,
   SECOND_11 = 11,
   SECOND_12 = 12,
   SECOND_13 = 13,
   SECOND_14 = 14,
   SECOND_15 = 15,
   SECOND_20 = 20,
   SECOND_25 = 25,
   SECOND_30 = 30,
   SECOND_35 = 35,
   SECOND_40 = 40,
   SECOND_45 = 45,
   SECOND_50 = 50,
   SECOND_55 = 55
};

sinput string s1 = "====== [PRO] 大周期趋势过滤器 (HTF Trend Filter) ======"; // 分组标题
input bool   InpEnableTrendFilter = true;           // [风控开关] 启用/禁用趋势过滤器 (Enable/Disable Trend Filter)
input ENUM_TIMEFRAMES InpMATimeframe = PERIOD_H1;   // [趋势周期] 用于判断趋势的MA时间周期 (MA Timeframe for Trend)
input int    InpMAPeriod = 200;                     // [趋势指标] MA的计算周期 (MA Period)
input ENUM_MA_METHOD InpMAMethod = MODE_EMA;        // [趋势指标] MA的计算方法 (MA Method: EMA, SMA, etc.)

sinput string s2 = "====== [PRO] 风险熔断机制 (Risk Circuit Breaker) ======"; // 分组标题
input bool   InpEnableGridLevelStop = true;         // [风控开关] 启用/禁用最大加仓层数止损 (Enable Max Grid Level Stop)
input int    InpMaxGridLevel = 7;                   // [风控上限] 允许的最大加仓次数 (Max addition orders allowed)

sinput string s3 = "====== [PRO] 手数管理模式 (Lot Management Mode) ======"; // 分组标题
input bool   InpUseLotMultiplier = true;            // [模式选择] true: 使用手数乘数模式, false: 使用原代码的固定手数数组
input double InpLotMultiplier = 1.4;                // [乘数模式] 手数递增乘数 (Lot multiplier for additions)
input double InpMaxLot = 5.0;                       // [手数上限] 任何单笔订单允许的最大手数 (Absolute Max Lot per Order)

sinput string s4 = "====== 基础交易参数 (Basic Trading Parameters) ======"; // 分组标题
input double InpDistanceMultiplier = 0.3; // 下单间距比例系数 (0.01-10)
input int InpOrderMultiplier = 1; // 下单倍数 (1-100)
input int InpInitialOrderDelay = 1; // 平仓后初始建仓延迟（秒）
input double 初始交易量 = 0.01;
input double 初始止盈 = 2.0;
input double 第一次加仓间隔 = 2.0;
input double 第二次加仓间隔 = 2.0;
input double 第三次加仓间隔 = 2.0;
input double 第四次加仓间隔 = 1.5;
input double 第一次加仓量 = 0.01;
input double 第二次加仓量 = 0.01;
input double 第三次加仓量 = 0.02;
input double 第四次加仓量 = 0.02;
input double 前5笔最终止盈 = 4.0;
input double 保本级别_0_15 = 2.5;
input double 止盈_0_15 = 4.5;
input double 保本级别_15_20 = 3.7;
input double 止盈_15_20 = 5.5;
input double 保本级别_20_25 = 4.5;
input double 止盈_20_25 = 6.0;
input double 保本级别_25_30 = 5.0;
input double 止盈_25_30 = 6.2;
input double 保本级别_30_35 = 5.5;
input double 止盈_30_35 = 6.5;
input double 保本级别_35_40 = 6.0;
input double 止盈_35_40 = 6.5;
input double 保本级别_40_45 = 6.5;
input double 止盈_40_45 = 7.0;
input string 禁止交易开始时间1 = "00:00";
input string 禁止交易结束时间1 = "00:01";
input string 禁止交易开始时间2 = "00:00";
input string 禁止交易结束时间2 = "00:01";
input string 禁止交易开始时间3 = "00:00";
input string 禁止交易结束时间3 = "00:01";
input int    InpDepth = 30;       // ZigZag深度
input int    InpDeviation = 12;   // ZigZag偏差
input int    InpBackstep = 2;     // ZigZag回溯步数
input color  InpUpColor = clrDarkGreen;   // 上升趋势颜色
input color  InpDownColor = clrBlue;      // 下跌趋势颜色
input color  InpSidewaysColor = clrTeal;  // 盘整趋势颜色
input double InpRangePercent = 0.03; // 盘整范围百分比
input int    InpZigZagLineWidth = 3; // ZigZag线段粗细
input ENUM_TRADE_DIRECTION InpUpTrendTradeDirection = TRADE_DIRECTION_BUY;    // 上升趋势交易方向
input ENUM_TRADE_DIRECTION InpDownTrendTradeDirection = TRADE_DIRECTION_SELL; // 下跌趋势交易方向
input ENUM_TRADE_DIRECTION InpSidewaysTrendTradeDirection = TRADE_DIRECTION_MFI; // 盘整趋势交易方向
input ENUM_TIMEFRAMES InpMFITimeframe = PERIOD_M1; // MFI时间周期
input int InpMFIPeriod = 9;           // MFI周期
input double InpMFIUpperLevel = 70.0; // MFI上限
input double InpMFILowerLevel = 35.0; // MFI下限
input bool   InpEnableNoTradeTime = false; // 启用禁止交易时间
input int    InpNoTradeHoursBefore = 4;   // 消息公布前禁止交易小时数
input int    InpNoTradeHoursAfter = 4;    // 消息公布后禁止交易小时数
input bool   InpNoTradeNextMonday = false; // 非农后下周一禁止交易
input string InpNoTradeNextMondayStart = "00:00"; // 下周一禁止交易开始时间
input string InpNoTradeNextMondayEnd = "22:00"; // 下周一禁止交易结束时间
input int    InpMaxDeviation = 50;        // 最大允许偏差(点)
input bool   InpEnableCandleTimeframe = false; // 启用蜡烛图时间框架功能
input ENUM_TIMEFRAMES InpCandleTimeframe = PERIOD_M4; // 蜡烛图时间框架
input double InpMaxAdditionDistance = 100.0; // 最大加仓间距（美元）
input double InpMaxAdditionsPerCandle = 1; // 每根蜡烛图最大加仓次数
input double InpAdditionalProfit = 4.0; // 自适应后动态额外利润（美元）
input bool InpDebugMode = false; // 调试模式
input int InpMaxHoldingHours = 2; // 最大持仓时间（小时）
input double InpMaxLoss = 299.0; // 最大亏损金额（美元）
input double InpMaxDistanceUSD = 999.0;     // 最大价格距离（美元）
input double InpDistanceMaxLoss = 601.0;  // 基于距离的最大亏损金额（美元）
input int InpMaxAdditionCount = 999;        // 最大加仓次数
input double InpAdditionMaxLoss = 199.0;  // 基于加仓的最大亏损金额（美元）
input bool   InpEnableReverseTrading = false;   // 启用反向交易
input double InpReverseTradeVolume = 0.01;     // 反向交易手数
input double InpReverseTradeDistance = 0.5;    // 反向交易距离（美元）
input double InpReverseTakeProfit = 1.5;       // 反向交易止盈（美元）
input double InpReverseAdditionDistance = 2.0; // 反向加仓间隔（美元）
input double InpReverseAdditionTakeProfit = 3.0; // 反向加仓止盈（美元）
input bool   InpEnableReverseNightClose = false;   // 启用反向订单夜间平仓
input string InpReverseCloseTime = "18:00";       // 反向订单平仓时间
input ENUM_SECOND_TIMEFRAME InpSecondTimeframe = SECOND_8; // 秒级时间周期
input bool InpEnableSecondTimeframe = true; // 启用秒级时间周期加仓
input int InpMaxAdditionsPerSecond = 1; // 秒级时间周期内最大加仓次数
input double InpSecondAdditionalProfit = 4.0; // 秒级自适应动态额外利润（美元）
input double InpHedgeDistance1 = 10.0; // 缓涨缓跌对冲距离1（美元）
input int InpHedgeAdditionCount1 = 24; // 缓涨缓跌对冲加仓次数1
input double InpHedgeDistance2 = 20.0; // 缓涨缓跌对冲距离2（美元）
input int InpHedgeAdditionCount2 = 32; // 缓涨缓跌对冲加仓次数2
input double InpHedgeDistance3 = 30.0; // 缓涨缓跌对冲距离3（美元）
input int InpHedgeAdditionCount3 = 44; // 缓涨缓跌对冲加仓次数3
input double InpVolatileDistance = 25.0; // 暴涨暴跌对冲距离（美元）
input int InpVolatileAdditionCount = 25; // 暴涨暴跌对冲加仓次数
input double InpMaxLossForHedge = -800.0; // 触发对冲的最大亏损（美元）
input double InpHedgeReboundDistance = 2.0; // 对冲平仓反弹距离（美元）
input double InpMinTotalVolume = 1.0; // 停止对冲的最小总持仓量
input double InpHedgeProfitMargin = 4.0; // 对冲后止盈额外利润（美元）
input double InpHedgeAdditionMultiplier = 0.25; // 对冲后加仓距离倍数
input int InpHedgeSecondTimeframe = 8;      // 对冲反弹秒级时间框架(秒)
input bool InpEnableHedgeSecondTimeframe = true; // 启用对冲反弹秒级时间框架

double AdditionLotSizes[] = {0.03,0.03,0.04,0.05,0.06,0.07,0.08,0.09,0.10,0.11,0.12,0.13,0.14,0.15,0.16,0.17,0.18,0.19,0.20,0.21,0.22,0.23,0.24,0.25,0.26,0.27,0.28,0.29,0.30,0.31,0.32,0.33,0.34,0.35,0.36,0.37,0.38,0.39,0.40,0.41,0.42,0.43,0.44,0.45,0.46,0.47,0.48,0.49,0.50,0.51,0.52,0.53,0.54,0.55,0.56,0.57,0.58,0.59,0.60,0.61,0.62,0.63,0.64,0.65,0.66,0.67,0.68,0.69,0.70};

// 在全局范围内定义LosingPosition结构
struct LosingPosition
{
   ulong ticket;
   double loss;
   int orderNumber;
};

// 添加秒级时间框架数据结构，专门用于对冲反弹
struct HedgeSecondData
{
   datetime time;           // 周期开始时间
   double openPrice;        // 开盘价
   double currentPrice;     // 当前价格
   bool isNewPeriod;        // 是否是新周期的第一个tick
};

HedgeSecondData hedgeSecondData;

CTrade trade;
CPositionInfo positionInfo;
CArrayObj trendSegments;

int MagicNumber = 123456;
ulong BuyTicket = 0, SellTicket = 0;
int AdditionCount = 0;
double InitialOrderPrice = 0;
double LastAdditionPrice = 0;
double NextAdditionPrice = 0;
ENUM_TRADE_DIRECTION CurrentDirection = TRADE_DIRECTION_BUY;
bool IsInitialTradeOpen = false;
bool IsMartingaleActive = false;

// 新增：大周期趋势过滤器相关变量
int trendMAHandle = INVALID_HANDLE;
datetime NoTradeStartTime, NoTradeEndTime;
bool IsInNoTradeTime = false;
bool ContinueExistingTrade = false;
double CurrentProtectionLevel = 2.5;
double CurrentTakeProfit = 4.0;
int zigzagHandle;
double zigzagBuffer[];
ENUM_TREND_TYPE currentTrend = TREND_SIDEWAYS;
ENUM_TREND_TYPE previousTrend = TREND_SIDEWAYS;
int lastAnalyzedBar = 0;
datetime lastTrendAnalysisTime = 0;
ENUM_TREND_TYPE cachedTrend = TREND_SIDEWAYS;
const int TREND_ANALYSIS_INTERVAL = 3600; // 每小时分析一次趋势
bool isInitialOrderPlaced = false;
datetime lastAdditionTime = 0;
int additionsThisCandle = 0;
double DynamicTakeProfit = 0; // 以美元为单位的动态止盈
double lastUpdatePrice = 0;
const double UPDATE_THRESHOLD = 0.5; // 0.5美元的价格变化阈值
double lastTotalDistance = 0;
const double DISTANCE_THRESHOLD = 1.0; // 1美元的距离变化阈值
double cachedStopLevel = 0;
int mfiHandle;
double mfiBuffer[];
ENUM_TRADE_DIRECTION lastMFIDirection = TRADE_DIRECTION_BUY;
datetime initialOrderTime = 0;
datetime lastLogTime = 0;
const int LOG_INTERVAL = 300; // 每5分钟记录一次日志
bool IsNoTradeTimeActive = false;
datetime NoTradeStartTime1, NoTradeEndTime1;
datetime NoTradeStartTime2, NoTradeEndTime2;
datetime NoTradeStartTime3, NoTradeEndTime3;
datetime lastNFPDate = 0;
ulong ReverseBuyTickets[];  // 存储反向买入订单号
ulong ReverseSellTickets[]; // 存储反向卖出订单号
double OriginalOrderPrices[];  // 存储原始订单的价格
ulong ReverseTradeTickets[];   // 存储反向交易的订单号
int ReverseOrderCount = 0; // 用于跟踪反向订单的总数
datetime lastSecondTimeframe = 0;
double lastSecondOpen = 0;
double currentSecondOpen = 0;
double currentSecondClose = 0;
double lastSecondClose = 0;
int additionsThisSecond = 0;
datetime resetTime; // 新添加的
bool IsHedgeActive = false;
double HedgeStartPrice = 0;
datetime lastPositionLogTime = 0;
int lastPositionCount = 0;
ulong InitialOrderTicket = 0;
int TotalAdditionCount = 0;
double TotalDistance = 0;
bool NeedRecheckHedge = false;
bool UseHedgeTakeProfit = false;
datetime LastHedgeTime = 0;
double AccumulatedHedgeProfit = 0.0;
datetime LastLogTime = 0;
datetime LastHedgeProfitLogTime = 0;

class CTrendSegment : public CObject
{
public:
   datetime    startTime;
   datetime    endTime;
   double      startPrice;
   double      endPrice;
   ENUM_TREND_TYPE  trend;
   
   CTrendSegment(datetime st, datetime et, double sp, double ep, ENUM_TREND_TYPE tr) 
      : startTime(st), endTime(et), startPrice(sp), endPrice(ep), trend(tr) {}
};

int OnInit()
{
   trade.SetExpertMagicNumber(MagicNumber);
   
   zigzagHandle = iCustom(_Symbol, PERIOD_CURRENT, "Examples\\ZigZag", InpDepth, InpDeviation, InpBackstep);
   if(zigzagHandle == INVALID_HANDLE)
   {
      Print("创建ZigZag指标句柄失败");
      return INIT_FAILED;
   }
   
   ArraySetAsSeries(zigzagBuffer, true);
   
   Print("成功加载 ", ArraySize(AdditionLotSizes), " 个加仓量");
   
   if(InpEnableNoTradeTime)
   {
      EventSetTimer(60); // 每分钟更新一次禁止交易时间状态
   }
   
   isInitialOrderPlaced = false;
   
   //下单间距比例系数
   if(InpDistanceMultiplier < 0.01 || InpDistanceMultiplier > 10)
   {
      Print("错误：下单间距比例系数必须在0.1到10之间");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // 验证下单倍数输入
   if(InpOrderMultiplier < 1 || InpOrderMultiplier > 100)
   {
      Print("错误：下单倍数必须在1到100之间");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   mfiHandle = iMFI(_Symbol, InpMFITimeframe, InpMFIPeriod, VOLUME_TICK);
   if(mfiHandle == INVALID_HANDLE)
   {
      Print("创建MFI指标句柄失败");
      return INIT_FAILED;
   }

   ArraySetAsSeries(mfiBuffer, true);

   // 初始化大周期趋势过滤器MA指标
   if(InpEnableTrendFilter)
   {
      trendMAHandle = iMA(_Symbol, InpMATimeframe, InpMAPeriod, 0, InpMAMethod, PRICE_CLOSE);
      if(trendMAHandle == INVALID_HANDLE)
      {
         Print("创建趋势过滤器MA指标句柄失败");
         return INIT_FAILED;
      }
      Print("趋势过滤器MA指标初始化成功 - 周期: ", EnumToString(InpMATimeframe), ", 期间: ", InpMAPeriod, ", 方法: ", EnumToString(InpMAMethod));
   }

   // 解析禁止交易时间
   MqlDateTime temp;
   TimeToStruct(TimeCurrent(), temp);
   
   string startTimeStr = StringFormat("%04d.%02d.%02d %s", temp.year, temp.mon, temp.day, 禁止交易开始时间1);
   string endTimeStr = StringFormat("%04d.%02d.%02d %s", temp.year, temp.mon, temp.day, 禁止交易结束时间1);
   
   NoTradeStartTime = StringToTime(startTimeStr);
   NoTradeEndTime = StringToTime(endTimeStr);

   // 如果结束时间小于开始时间，认为它是第二天的时间
   if(NoTradeEndTime <= NoTradeStartTime)
   {
      NoTradeEndTime += 86400; // 加上一天的秒数
   }

   Print("禁止交易时间1设置为：", TimeToString(NoTradeStartTime), " - ", TimeToString(NoTradeEndTime));
   
   // 初始化所有禁止交易时间
   UpdateNoTradeTime(true);
   
   // 初始化时立即更新一次禁止交易时间状态
   if(InpEnableNoTradeTime)
   {
      UpdateSpecialNoTradeTime();
   }
   
   ArrayResize(OriginalOrderPrices, 0);
   ArrayResize(ReverseTradeTickets, 0);
   
   // 在函数末尾添加对hedgeSecondData的初始化
   hedgeSecondData.time = 0;
   hedgeSecondData.openPrice = 0;
   hedgeSecondData.currentPrice = 0;
   hedgeSecondData.isNewPeriod = false;
   
   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   IndicatorRelease(zigzagHandle);
   ObjectsDeleteAll(0, "TrendArea_");
   ObjectsDeleteAll(0, "TrendLabel_");
   ObjectsDeleteAll(0, "ZigZag_");
   ObjectsDeleteAll(0, "TrendNumber_");
   trendSegments.Clear();
   EventKillTimer();
   IndicatorRelease(mfiHandle);

   // 释放趋势过滤器MA指标句柄
   if(trendMAHandle != INVALID_HANDLE)
   {
      IndicatorRelease(trendMAHandle);
   }
}

void OnTick()
{
   // 新增：网格层数硬止损检查 - 在所有其他逻辑之前执行
   if(InpEnableGridLevelStop && AdditionCount >= InpMaxGridLevel)
   {
      Print("达到最大加仓层数 (", AdditionCount, "/", InpMaxGridLevel, ")，执行熔断止损");
      CloseAllPositions();
      return;
   }

   // 从原有代码开始部分，添加对对冲秒级时间框架数据的更新
   if(IsHedgeActive && InpEnableHedgeSecondTimeframe)
   {
      UpdateHedgeSecondTimeframeData();
   }

   static datetime lastCheckTime = 0;
   datetime currentTime = TimeCurrent();

   // 每5秒检查一次是否所有仓位已平仓
   if(currentTime - lastCheckTime >= 5)
   {
      if(!HasOpenPositions() && (IsInitialTradeOpen || IsMartingaleActive))
      {
         Print("检测到所有仓位已平仓，准备开始新的交易周期。");
         PrepareForNewTradeCycle();
      }
      lastCheckTime = currentTime;
   }
   
   // 添加每分钟输出一次累计可用利润的日志
   if(currentTime - LastHedgeProfitLogTime >= 60) // 每60秒输出一次
   {
      if(AccumulatedHedgeProfit > 0 || HasOpenPositions())
      {
         Print("累计可用对冲利润: ", DoubleToString(AccumulatedHedgeProfit, 2), 
               ", 总持仓量: ", DoubleToString(GetTotalPositionVolume(), 2), 
               ", 总盈亏: ", DoubleToString(CalculateTotalLoss(), 2));
      }
      LastHedgeProfitLogTime = currentTime;
   }
   
   // 定期显示EA状态信息
   DisplayStatusInfo();

   static int lastDay = -1;
   static datetime lastNoTradeLogTime = 0;
   MqlDateTime current;
   TimeToStruct(currentTime, current);
   
   if(current.day != lastDay)
   {
      // 更新常规禁止交易时间
      UpdateNoTradeTime(true);
      lastDay = current.day;
   }

   if(InpEnableNoTradeTime)
   {
      // 更新包括非农在内的特殊禁止交易时间
      UpdateSpecialNoTradeTime();
   }

   bool isNoTradeTime = IsNoTradeTime() || IsInNoTradeTime;

   if(isNoTradeTime)
   {
      if(currentTime - lastNoTradeLogTime >= 300) // 300秒 = 5分钟
      {
         Print("当前时间在禁止交易时间范围内，不执行交易操作");
         lastNoTradeLogTime = currentTime;
      }
   }

   // 更新秒级时间周期数据
   UpdateSecondTimeframeData();
   
   bool shouldAnalyzeTrend = false;
   
   if(!IsInitialTradeOpen || !HasOpenPositions())
   {
      shouldAnalyzeTrend = true;
   }
   else if(currentTime - lastTrendAnalysisTime >= TREND_ANALYSIS_INTERVAL)
   {
      shouldAnalyzeTrend = true;
   }
   
   if(shouldAnalyzeTrend)
   {
      int bars = Bars(_Symbol, PERIOD_CURRENT);
      if(bars != lastAnalyzedBar)
      {
         if(CopyBuffer(zigzagHandle, 0, 0, bars, zigzagBuffer) <= 0)
         {
            Print("复制ZigZag缓冲区失败");
            return;
         }
         
         AnalyzeTrend();
         DisplayTrendAreas();
         DrawZigZag();
         
         lastAnalyzedBar = bars;
         lastTrendAnalysisTime = currentTime;
      }
   }

   if(!isNoTradeTime)
   {
      if(!HasOpenPositions() && currentTime >= resetTime)
      {
         Print("尝试开立初始仓位，当前时间：", TimeToString(currentTime), ", 重置时间：", TimeToString(resetTime));
         if(OpenInitialPositions())
         {
            Print("初始建仓成功");
         }
         else
         {
            resetTime = currentTime + InpInitialOrderDelay;
            Print("初始建仓失败，将在 ", TimeToString(resetTime, TIME_SECONDS), " 后再次尝试");
         }
      }
      else if(HasOpenPositions())
      {
         ManagePositions();
      }
   }
   else
   {
      if(HasOpenPositions())
      {
         ManagePositions();
      }
   }
   
   // 检查并平仓反向订单（如果启用）
   CheckAndCloseReversePositions();
   
   // 风险管理检查（无论是否在交易时间内都执行）
   CheckRiskManagement();
   
   // 新增：检查是否需要执行对冲策略
   if(NeedRecheckHedge || HasOpenPositions())
   {
      double totalVolume = GetTotalPositionVolume();
      double totalLoss = CalculateTotalLoss();
      
      if(totalVolume >= InpMinTotalVolume || totalLoss <= InpMaxLossForHedge)
      {
         CheckHedgeConditions();
      }
      
      NeedRecheckHedge = false;
   }
   
   // 如果对冲策略激活，管理对冲仓位
   if(IsHedgeActive)
   {
      ManageHedgePositions();
   }
}

void PrepareForNewTradeCycle()
{
   IsInitialTradeOpen = false;
   IsMartingaleActive = false;
   AdditionCount = 0;
   InitialOrderPrice = 0;
   LastAdditionPrice = 0;
   NextAdditionPrice = 0;
   isInitialOrderPlaced = false;
   resetTime = TimeCurrent() + InpInitialOrderDelay;
   
   // 重置对冲相关变量
   IsHedgeActive = false;
   HedgeStartPrice = 0;
   UseHedgeTakeProfit = false;
   AccumulatedHedgeProfit = 0.0;  // 重置累计利润
   
   Print("准备开始新的交易周期。下次初始建仓时间: ", TimeToString(resetTime));
}

void UpdateSecondTimeframeData()
{
   if(!InpEnableSecondTimeframe)
      return;

   datetime currentTime = TimeCurrent();
   
   // 检查是否到达新的秒级时间周期
   if(currentTime - lastSecondTimeframe >= InpSecondTimeframe)
   {
      // 记录上一个周期的收盘价
      currentSecondClose = SymbolInfoDouble(_Symbol, SYMBOL_BID);

      // 如果不是第一次更新，输出上一周期的日志，但减少频率
      static datetime lastSecondLogTime = 0;
      if(lastSecondTimeframe != 0 && currentTime - lastSecondLogTime >= 60) // 每60秒输出一次
      {
         Print("秒级时间周期结束 - 时间: ", TimeToString(lastSecondTimeframe), 
               ", 开盘价: ", DoubleToString(currentSecondOpen, _Digits),
               ", 收盘价: ", DoubleToString(currentSecondClose, _Digits));
         lastSecondLogTime = currentTime;
      }

      // 开始新的周期
      lastSecondTimeframe = currentTime - (currentTime % InpSecondTimeframe);
      currentSecondOpen = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      additionsThisSecond = 0;

      // 输出新周期开始的日志，但减少频率
      if(currentTime - lastSecondLogTime >= 60) // 使用同一个时间标志
      {
         Print("新的秒级时间周期开始 - 时间: ", TimeToString(lastSecondTimeframe), 
               ", 开盘价: ", DoubleToString(currentSecondOpen, _Digits));
      }
   }
   else
   {
      // 更新当前周期的收盘价
      currentSecondClose = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   }
}

// 更新对冲秒级时间框架数据
void UpdateHedgeSecondTimeframeData()
{
   if(!InpEnableHedgeSecondTimeframe)
      return;

   datetime currentTime = TimeCurrent();
   
   // 检查是否到达新的秒级时间周期
   if(currentTime - hedgeSecondData.time >= InpHedgeSecondTimeframe)
   {
      // 开始新的周期
      hedgeSecondData.time = currentTime - (currentTime % InpHedgeSecondTimeframe);
      hedgeSecondData.openPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                                ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                                : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      hedgeSecondData.currentPrice = hedgeSecondData.openPrice;
      hedgeSecondData.isNewPeriod = true;
      
      static datetime lastHedgeLogTime = 0;
      if(currentTime - lastHedgeLogTime >= 60) // 每60秒输出一次日志
      {
         Print("新的对冲秒级时间周期开始 - 时间: ", TimeToString(hedgeSecondData.time), 
               ", 开盘价: ", DoubleToString(hedgeSecondData.openPrice, _Digits));
         lastHedgeLogTime = currentTime;
      }
   }
   else
   {
      // 更新当前周期的价格
      hedgeSecondData.currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                                  ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                                  : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      hedgeSecondData.isNewPeriod = false;
   }
}

// 检查对冲秒级时间框架条件
bool CheckHedgeSecondTimeframeCondition()
{
   if(!InpEnableHedgeSecondTimeframe)
      return true; // 如果未启用，则默认满足条件
   
   if(hedgeSecondData.time == 0 || hedgeSecondData.isNewPeriod)
      return false; // 如果是新周期的第一个tick或未初始化，不满足条件
   
   bool condition = false;
   
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      // 买入方向下，当前价格应低于开盘价（表示下跌，有利于买单获利）
      condition = (hedgeSecondData.currentPrice < hedgeSecondData.openPrice);
   }
   else
   {
      // 卖出方向下，当前价格应高于开盘价（表示上涨，有利于卖单获利）
      condition = (hedgeSecondData.currentPrice > hedgeSecondData.openPrice);
   }
   
   return condition;
}

void DisplayStatusInfo()
{
   static datetime lastStatusLogTime = 0;
   datetime currentTime = TimeCurrent();
   
   if(currentTime - lastStatusLogTime >= 60) // 每60秒输出一次
   {
      double totalVolume = GetTotalPositionVolume();
      double totalLoss = CalculateTotalLoss();
      
      Print("EA状态信息 - ",
            "总持仓量: ", DoubleToString(totalVolume, 2),
            ", 总盈亏: ", DoubleToString(totalLoss, 2),
            ", 加仓次数: ", AdditionCount,
            ", 累计对冲利润: ", DoubleToString(AccumulatedHedgeProfit, 2),
            ", 对冲模式: ", (IsHedgeActive ? "活跃" : "非活跃"),
            ", 对冲止盈模式: ", (UseHedgeTakeProfit ? "启用" : "未启用")
           );
      
      lastStatusLogTime = currentTime;
   }
}

void CheckAndCloseReversePositions()
{
   if(!InpEnableReverseNightClose)
      return;

   datetime currentTime = TimeCurrent();
   MqlDateTime struct_time;
   TimeToStruct(currentTime, struct_time);

   int closeHour, closeMinute;
   string closeTimeStr = InpReverseCloseTime;
   if(StringLen(closeTimeStr) == 5 && StringFind(closeTimeStr, ":") == 2)
   {
      closeHour = (int)StringToInteger(StringSubstr(closeTimeStr, 0, 2));
      closeMinute = (int)StringToInteger(StringSubstr(closeTimeStr, 3, 2));
   }
   else
   {
      Print("无效的平仓时间格式。使用默认值 18:00");
      closeHour = 18;
      closeMinute = 0;
   }

   if(struct_time.hour == closeHour && struct_time.min == closeMinute)
   {
      CloseAllReversePositions();
   }
}

void CloseAllReversePositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_SELL) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_BUY))
         {
            trade.PositionClose(positionInfo.Ticket());
            Print("已平仓反向订单：", positionInfo.Ticket());
         }
      }
   }
}

void UpdateNoTradeTime(bool isInit = false)
{
   MqlDateTime current;
   TimeToStruct(TimeCurrent(), current);

   UpdateNoTradeTimePair(禁止交易开始时间1, 禁止交易结束时间1, NoTradeStartTime1, NoTradeEndTime1, current);
   UpdateNoTradeTimePair(禁止交易开始时间2, 禁止交易结束时间2, NoTradeStartTime2, NoTradeEndTime2, current);
   UpdateNoTradeTimePair(禁止交易开始时间3, 禁止交易结束时间3, NoTradeStartTime3, NoTradeEndTime3, current);

   Print("更新禁止交易时间1：", TimeToString(NoTradeStartTime1), " - ", TimeToString(NoTradeEndTime1));
   Print("更新禁止交易时间2：", TimeToString(NoTradeStartTime2), " - ", TimeToString(NoTradeEndTime2));
   Print("更新禁止交易时间3：", TimeToString(NoTradeStartTime3), " - ", TimeToString(NoTradeEndTime3));
   
   // 如果不是初始化，还需要更新特殊时间（如非农报告时间）
   if(!isInit)
   {
      UpdateSpecialNoTradeTime();
   }
}

void UpdateNoTradeTimePair(string startTimeStr, string endTimeStr, datetime &outStartTime, datetime &outEndTime, MqlDateTime &current)
{
   string fullStartTimeStr = StringFormat("%04d.%02d.%02d %s", current.year, current.mon, current.day, startTimeStr);
   string fullEndTimeStr = StringFormat("%04d.%02d.%02d %s", current.year, current.mon, current.day, endTimeStr);

   outStartTime = StringToTime(fullStartTimeStr);
   outEndTime = StringToTime(fullEndTimeStr);

   if(outEndTime <= outStartTime)
   {
      outEndTime += 86400; // 如果结束时间小于开始时间，加上一天的秒数
   }
}

// 检查是否在禁止交易时间内
bool IsNoTradeTime()
{
   datetime currentTime = TimeCurrent();
   
   if(IsTimeInRange(currentTime, NoTradeStartTime1, NoTradeEndTime1) ||
      IsTimeInRange(currentTime, NoTradeStartTime2, NoTradeEndTime2) ||
      IsTimeInRange(currentTime, NoTradeStartTime3, NoTradeEndTime3))
   {
      return true;
   }
   
   return false;
}

bool IsTimeInRange(datetime time, datetime rangeStart, datetime rangeEnd)
{
   if(rangeEnd > rangeStart)
   {
      return (time >= rangeStart && time <= rangeEnd);
   }
   else // 跨越午夜的情况
   {
      return (time >= rangeStart || time <= rangeEnd);
   }
}

void OnTimer()
{
   if(InpEnableNoTradeTime)
   {
      UpdateSpecialNoTradeTime();
   }
}

datetime CalculateTradeHours(datetime startTime, int hours)
{
   datetime endTime = startTime;
   int hoursAdded = 0;
   
   while(hoursAdded < hours)
   {
      endTime += 3600; // 添加一小时
      MqlDateTime dt;
      TimeToStruct(endTime, dt);
      
      // 如果是周末，跳过
      if(dt.day_of_week == 0 || dt.day_of_week == 6)
      {
         continue;
      }
      
      // 如果是工作日的非交易时间（假设交易时间为00:00-23:59），跳过
      if(dt.hour >= 0 && dt.hour < 24)
      {
         hoursAdded++;
      }
   }
   
   return endTime;
}

void UpdateSpecialNoTradeTime()
{
   static datetime lastNoTradeLogTime = 0;
   datetime currentTime = TimeCurrent();
   MqlDateTime currentMqlTime;
   TimeToStruct(currentTime, currentMqlTime);
   
   IsInNoTradeTime = false;
   
   string noTradeReason = "";
   datetime noTradeStart = 0, noTradeEnd = 0;
   
   // 检查是否是非农就业报告日
   if(currentMqlTime.day_of_week == 5 && currentMqlTime.day <= 7)
   {
      datetime nfpTime = StringToTime(StringFormat("%04d.%02d.%02d %s", 
         currentMqlTime.year, currentMqlTime.mon, currentMqlTime.day, 
         (currentMqlTime.mon >= 4 && currentMqlTime.mon <= 10) ? "14:30" : "15:30"));
      
      // 非农当天的禁止交易时间
      noTradeStart = CalculateTradeHours(nfpTime, -InpNoTradeHoursBefore);
      noTradeEnd = CalculateTradeHours(nfpTime, InpNoTradeHoursAfter);
      
      if(currentTime >= noTradeStart && currentTime <= noTradeEnd)
      {
         IsInNoTradeTime = true;
         noTradeReason = "非农就业报告当天";
      }
      
      // 更新上一次非农报告的日期
      lastNFPDate = nfpTime;
   }
   // 检查是否是非农后的下一个星期一
   else if(InpNoTradeNextMonday && currentMqlTime.day_of_week == 1 && lastNFPDate != 0)
   {
      // 计算非农报告后的下一个星期一的日期
      datetime nextMonday = lastNFPDate + (3 * 24 * 60 * 60); // 非农后的第三天
      MqlDateTime nextMondayMql;
      TimeToStruct(nextMonday, nextMondayMql);
      
      // 确保我们只限制非农后的下一个星期一
      if(currentMqlTime.year == nextMondayMql.year && 
         currentMqlTime.mon == nextMondayMql.mon && 
         currentMqlTime.day == nextMondayMql.day)
      {
         datetime mondayStart = StringToTime(StringFormat("%04d.%02d.%02d %s", currentMqlTime.year, currentMqlTime.mon, currentMqlTime.day, InpNoTradeNextMondayStart));
         datetime mondayEnd = StringToTime(StringFormat("%04d.%02d.%02d %s", currentMqlTime.year, currentMqlTime.mon, currentMqlTime.day, InpNoTradeNextMondayEnd));
         
         if(currentTime >= mondayStart && currentTime <= mondayEnd)
         {
            IsInNoTradeTime = true;
            noTradeReason = "非农就业报告后的下一个周一";
            noTradeStart = mondayStart;
            noTradeEnd = mondayEnd;
         }
      }
   }
   
   // 检查是否是每月4、5、6、7、8、10、13、17、21、22、23、24、25、26日的特定时间
   if(!IsInNoTradeTime)
   {
      int specialDays[] = {};
      for(int i=0; i<ArraySize(specialDays); i++)
      {
         if(currentMqlTime.day == specialDays[i])
         {datetime specialTime = StringToTime(StringFormat("%04d.%02d.%02d %s", 
               currentMqlTime.year, currentMqlTime.mon, currentMqlTime.day, 
               (currentMqlTime.mon >= 4 && currentMqlTime.mon <= 10) ? "14:30" : "15:30"));
            
            if(currentTime >= (specialTime - InpNoTradeHoursBefore * 3600) && 
               currentTime <= (specialTime + InpNoTradeHoursAfter * 3600))
            {
               IsInNoTradeTime = true;
               noTradeReason = "特殊日期 " + IntegerToString(specialDays[i]);
               noTradeStart = specialTime - InpNoTradeHoursBefore * 3600;
               noTradeEnd = specialTime + InpNoTradeHoursAfter * 3600;
               break;
            }
         }
      }
   }
   
   // 修改输出禁止交易时间信息的部分
   if(IsInNoTradeTime && currentTime - lastNoTradeLogTime >= 300) // 每5分钟最多输出一次
   {
      Print("禁止交易时间: ", TimeToString(noTradeStart), " - ", TimeToString(noTradeEnd), 
            ", 原因: ", noTradeReason);
      lastNoTradeLogTime = currentTime;
   }
}

bool HasOpenPositions()
{
   int count = 0;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         count++;
      }
   }
   
   datetime currentTime = TimeCurrent();
   if(count != lastPositionCount || currentTime - lastPositionLogTime >= 300) // 每5分钟或仓位数量变化时输出日志
   {
      Print("检测到 ", count, " 个开放的仓位");
      lastPositionLogTime = currentTime;
      lastPositionCount = count;
   }
   
   return count > 0;
}

bool OpenInitialPositions()
{
   if(isInitialOrderPlaced || HasOpenPositions())
   {
      Print("已有初始订单或开放仓位，不再开立新仓位");
      return false;
   }

   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // 计算每点价值
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double pointValue = tickValue / tickSize;

   // 计算止盈点数
   int tpPoints = (int)(初始止盈 * InpDistanceMultiplier / pointValue);
   
   ENUM_TRADE_DIRECTION tradeDirection = GetTradeDirection();
   
   Print("尝试开立初始仓位，交易方向：", EnumToString(tradeDirection));

   bool success = false;

   if(tradeDirection == TRADE_DIRECTION_BUY || tradeDirection == TRADE_DIRECTION_BOTH)
   {
      // 检查趋势过滤器
      if(CheckTrendFilter(ORDER_TYPE_BUY))
      {
         if(trade.Buy(初始交易量 * InpOrderMultiplier, _Symbol, ask, 0, NormalizeDouble(ask + tpPoints * _Point, _Digits), "初始买入"))
         {
            BuyTicket = trade.ResultOrder();
            InitialOrderPrice = ask;
            ArrayResize(OriginalOrderPrices, ArraySize(OriginalOrderPrices) + 1);
            OriginalOrderPrices[0] = ask;
            IsInitialTradeOpen = true;
            isInitialOrderPlaced = true;
            initialOrderTime = TimeCurrent();
            Print("初始买入仓位已开立。单号: ", BuyTicket, ", 止盈: ", NormalizeDouble(ask + tpPoints * _Point, _Digits));
            success = true;
         }
         else
         {
            Print("初始买入仓位开立失败。错误代码：", GetLastError(), ", 描述：", GetErrorDescription(GetLastError()));
         }
      }
      else
      {
         Print("趋势过滤器阻止初始买入操作");
      }
   }
   
   if(tradeDirection == TRADE_DIRECTION_SELL || tradeDirection == TRADE_DIRECTION_BOTH)
   {
      // 检查趋势过滤器
      if(CheckTrendFilter(ORDER_TYPE_SELL))
      {
         if(trade.Sell(初始交易量 * InpOrderMultiplier, _Symbol, bid, 0, NormalizeDouble(bid - tpPoints * _Point, _Digits), "初始卖出"))
         {
            SellTicket = trade.ResultOrder();
            InitialOrderPrice = bid;
            if(tradeDirection == TRADE_DIRECTION_BOTH)
            {
               ArrayResize(OriginalOrderPrices, 2);
               OriginalOrderPrices[1] = bid;
            }
            else
            {
               ArrayResize(OriginalOrderPrices, 1);
               OriginalOrderPrices[0] = bid;
            }
            IsInitialTradeOpen = true;
            isInitialOrderPlaced = true;
            initialOrderTime = TimeCurrent();
            Print("初始卖出仓位已开立。单号: ", SellTicket, ", 止盈: ", NormalizeDouble(bid - tpPoints * _Point, _Digits));
            success = true;
         }
         else
         {
            Print("初始卖出仓位开立失败。错误代码：", GetLastError(), ", 描述：", GetErrorDescription(GetLastError()));
         }
      }
      else
      {
         Print("趋势过滤器阻止初始卖出操作");
      }
   }
   
   if(success)
   {
      LastAdditionPrice = InitialOrderPrice;
      InitialOrderTicket = (CurrentDirection == TRADE_DIRECTION_BUY) ? BuyTicket : SellTicket;
      TotalAdditionCount = 0;
      Print("初始仓位开立成功");
   }
   else
   {
      Print("未能开立任何初始仓位，请检查交易条件和参数设置");
   }

   return success;
}

// 新增：大周期趋势过滤器检查函数
bool CheckTrendFilter(ENUM_ORDER_TYPE orderType)
{
   if(!InpEnableTrendFilter || trendMAHandle == INVALID_HANDLE)
   {
      return true; // 如果未启用趋势过滤器或句柄无效，允许交易
   }

   double maBuffer[1];
   if(CopyBuffer(trendMAHandle, 0, 0, 1, maBuffer) <= 0)
   {
      Print("获取趋势过滤器MA数据失败");
      return true; // 如果获取数据失败，允许交易以避免阻塞
   }

   double currentPrice = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double maValue = maBuffer[0];

   bool allowTrade = false;

   if(orderType == ORDER_TYPE_BUY)
   {
      // 买入：只有当前价格高于MA时才允许
      allowTrade = (currentPrice > maValue);
      if(!allowTrade)
      {
         Print("趋势过滤器阻止买入 - 当前价格 (", DoubleToString(currentPrice, _Digits), ") 低于MA (", DoubleToString(maValue, _Digits), ")");
      }
   }
   else // ORDER_TYPE_SELL
   {
      // 卖出：只有当前价格低于MA时才允许
      allowTrade = (currentPrice < maValue);
      if(!allowTrade)
      {
         Print("趋势过滤器阻止卖出 - 当前价格 (", DoubleToString(currentPrice, _Digits), ") 高于MA (", DoubleToString(maValue, _Digits), ")");
      }
   }

   return allowTrade;
}

// 新增：获取最后一笔持仓的手数
double GetLastPositionLotSize()
{
   double lastLotSize = 0;
   datetime lastTime = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         // 检查是否是当前交易方向的持仓
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_BUY) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_SELL))
         {
            datetime posTime = positionInfo.Time();
            if(posTime > lastTime)
            {
               lastTime = posTime;
               lastLotSize = positionInfo.Volume();
            }
         }
      }
   }

   return lastLotSize;
}

bool TryOrderSend(ENUM_ORDER_TYPE orderType)
{
   if(isInitialOrderPlaced)
   {
      Print("初始订单已经下达，不再尝试下单");
      return false;
   }

   double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // 直接使用点数设置止盈
   int tpPoints = (int)(初始止盈 * 100); // 将美元乘以100转换为点数
   
   double tp = NormalizeDouble(price + ((orderType == ORDER_TYPE_BUY) ? 1 : -1) * tpPoints * _Point, _Digits);
   
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = 初始交易量 * InpOrderMultiplier;
   request.type = orderType;
   request.price = price;
   request.deviation = InpMaxDeviation;
   request.tp = tp;
   request.sl = 0; // 不设置止损
   request.comment = (orderType == ORDER_TYPE_BUY) ? "初始买入" : "初始卖出";
   request.type_filling = ORDER_FILLING_IOC;
   request.magic = MagicNumber;
   
   Print("调试信息 - 订单类型: ", EnumToString(orderType), ", 价格: ", price, ", 止盈: ", tp, ", 止盈点数: ", tpPoints);
   
   if(!OrderSend(request, result))
   {
      int error = GetLastError();
      Print("订单发送失败，错误代码：", error, "，描述：", GetErrorDescription(error));
      return false;
   }
   
   if(result.retcode == TRADE_RETCODE_DONE)
   {
      Print("订单发送成功，订单号：", result.order, "，止盈价格：", tp, "，止盈点数：", tpPoints);
      if(orderType == ORDER_TYPE_BUY)
      {
         BuyTicket = result.order;
         InitialOrderPrice = price;
      }
      else
      {
         SellTicket = result.order;
         InitialOrderPrice = price;
      }
      IsInitialTradeOpen = true;
      isInitialOrderPlaced = true;
      initialOrderTime = TimeCurrent();
      return true;
   }
   else
   {
      Print("订单执行失败，返回代码：", result.retcode, "，描述：", GetErrorDescription(result.retcode));
      return false;
   }
}

void ManagePositions()
{
   if(!HasOpenPositions())
   {
      return;
   }

   if(!PositionSelectByTicket(BuyTicket) && !PositionSelectByTicket(SellTicket))
   {
      ResetTrade();
      return;
   }
   
   // 处理原始交易方向的变化
   if(!PositionSelectByTicket(BuyTicket) && !IsMartingaleActive)
   {
      CurrentDirection = TRADE_DIRECTION_SELL;
      IsMartingaleActive = true;
      LastAdditionPrice = InitialOrderPrice;
      Print("买入仓位已平仓。开始卖出方向的马丁格尔策略。");
   
   }
   else if(!PositionSelectByTicket(SellTicket) && !IsMartingaleActive)
   {
      CurrentDirection = TRADE_DIRECTION_BUY;
      IsMartingaleActive = true;
      LastAdditionPrice = InitialOrderPrice;
      Print("卖出仓位已平仓。开始买入方向的马丁格尔策略。");
   }
   
   // 管理原始交易（包括加仓）
   ManageAdditions();
   
   // 更新原始交易的止盈
   UpdateAllPositionsTakeProfit();
   
   // 管理反向交易
   if(InpEnableReverseTrading)
   {
      ManageReverseTrading();
   }
   
   // 检查是否需要执行对冲策略
   CheckHedgeConditions();
   
   // 如果对冲策略激活，管理对冲仓位
   if(IsHedgeActive)
   {
      ManageHedgePositions();
   }
}

void ManageAdditions()
{
   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double priceDifference;
   
   double totalLots = CalculateTotalLots();
   double totalLoss = CalculateTotalLoss();
   
   // 只在非对冲状态下更新保护级别和止盈
   if(!UseHedgeTakeProfit)
   {
      UpdateProtectionLevelAndTakeProfit();
   }
   
   // 减少日志输出频率
   datetime currentTime = TimeCurrent();
   if(currentTime - LastLogTime >= 60) // 每60秒输出一次
   {
      Print("管理加仓 - 当前价格: ", DoubleToString(currentPrice, _Digits), 
            ", 最后加仓价格: ", DoubleToString(LastAdditionPrice, _Digits),
            ", 加仓次数: ", AdditionCount,
            ", 总持仓量: ", DoubleToString(totalLots, 2),
            ", 总盈亏: ", DoubleToString(totalLoss, 2));
      LastLogTime = currentTime;
   }

   if(AdditionCount < 4)
   {
      double additionInterval = 0;
      double additionLotSize = 0;
      
      switch(AdditionCount)
      {
         case 0: additionInterval = 第一次加仓间隔 * InpDistanceMultiplier; additionLotSize = 第一次加仓量; break;
         case 1: additionInterval = 第二次加仓间隔 * InpDistanceMultiplier; additionLotSize = 第二次加仓量; break;
         case 2: additionInterval = 第三次加仓间隔 * InpDistanceMultiplier; additionLotSize = 第三次加仓量; break;
         case 3: additionInterval = 第四次加仓间隔 * InpDistanceMultiplier; additionLotSize = 第四次加仓量; break;
      }
      
      priceDifference = (CurrentDirection == TRADE_DIRECTION_BUY) ? LastAdditionPrice - currentPrice : currentPrice - LastAdditionPrice;
      
      // 减少日志输出频率，与前面保持一致
      if(currentTime - LastLogTime >= 0) // 使用同一时间标志，但这里实际不需要再判断时间
      {
         Print("检查是否需要加仓 - 价格差: ", DoubleToString(priceDifference, _Digits), 
               ", 加仓间隔: ", DoubleToString(additionInterval, _Digits));
      }

      if(priceDifference >= additionInterval)
      {
         bool canAddPosition = true;
         
         if(InpEnableSecondTimeframe)
         {
            canAddPosition = CheckSecondTimeframeCondition() && additionsThisSecond < InpMaxAdditionsPerSecond;
         }
         else if(InpEnableCandleTimeframe)
         {
            canAddPosition = CanAddPosition();
         }
         
         if(canAddPosition)
         {
            AddPosition(additionLotSize * InpOrderMultiplier, AdditionCount + 1);
            
            if(AdditionCount == 4)
            {
               NextAdditionPrice = CalculateNextAdditionPrice(totalLots, totalLoss);
               Print("设置下一次加仓价格: ", DoubleToString(NextAdditionPrice, _Digits));
            }
         }
      }
   }
   else
   {
      priceDifference = MathAbs(currentPrice - LastAdditionPrice);
      
      bool canAddPosition = false;
      
      // 检查蜡烛图条件
      if(!InpEnableCandleTimeframe || CheckCandleCondition())
      {
         canAddPosition = true;
      }
      
      // 检查最大加仓间距
      if(priceDifference >= InpMaxAdditionDistance * InpDistanceMultiplier)
      {
         canAddPosition = true;
      }
      
      // 减少日志输出频率，与前面保持一致
      if(currentTime - LastLogTime >= 0) // 使用同一时间标志
      {
         Print("检查是否需要继续加仓 - 价格差: ", DoubleToString(priceDifference, _Digits), 
               ", 下一次加仓价格: ", DoubleToString(NextAdditionPrice, _Digits),
               ", 是否可以加仓: ", canAddPosition ? "是" : "否");
      }

      if(canAddPosition && 
         ((CurrentDirection == TRADE_DIRECTION_BUY && currentPrice <= NextAdditionPrice) ||
          (CurrentDirection == TRADE_DIRECTION_SELL && currentPrice >= NextAdditionPrice)))
      {
         bool shouldAdd = true;
         
         if(InpEnableSecondTimeframe)
         {
            shouldAdd = CheckSecondTimeframeCondition() && additionsThisSecond < InpMaxAdditionsPerSecond;
         }
         else if(InpEnableCandleTimeframe)
         {
            shouldAdd = CanAddPosition();
         }
         
         if(shouldAdd)
         {
            int index = AdditionCount - 4;
            if(index < ArraySize(AdditionLotSizes))
            {
               AddPosition(AdditionLotSizes[index] * InpOrderMultiplier, AdditionCount + 1);
               NextAdditionPrice = CalculateNextAdditionPrice(totalLots + AdditionLotSizes[index] * InpOrderMultiplier, totalLoss);
               Print("设置下一次加仓价格: ", DoubleToString(NextAdditionPrice, _Digits));
            }
            else
            {
               Print("警告：已达到最大加仓次数，无法继续加仓");
            }
         }
      }
   }
   
   // 只有在非对冲模式下才检查动态止盈
   if(!UseHedgeTakeProfit)
   {
      CheckForTakeProfit();
   }
}

bool CheckSecondTimeframeCondition()
{
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      return (currentSecondClose > currentSecondOpen);
   }
   else if(CurrentDirection == TRADE_DIRECTION_SELL)
   {
      return (currentSecondClose < currentSecondOpen);
   }
   return false;
}

void CheckHedgeConditions()
{
   if(IsHedgeActive)
      return;

   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                         : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double totalDistance = MathAbs(currentPrice - InitialOrderPrice);
   double totalLoss = CalculateTotalLoss();
   double totalVolume = GetTotalPositionVolume();

   Print("检查对冲条件 - 总距离: ", DoubleToString(totalDistance, 2), 
         ", 加仓次数: ", TotalAdditionCount, 
         ", 总亏损: ", DoubleToString(totalLoss, 2),
         ", 总持仓量: ", DoubleToString(totalVolume, 2));

   // 检查缓涨缓跌条件
   if((totalDistance <= InpHedgeDistance1 && TotalAdditionCount >= InpHedgeAdditionCount1) ||
      (totalDistance <= InpHedgeDistance2 && TotalAdditionCount >= InpHedgeAdditionCount2) ||
      (totalDistance <= InpHedgeDistance3 && TotalAdditionCount >= InpHedgeAdditionCount3))
   {
      ActivateHedge("缓涨缓跌");
   }
   // 检查暴涨暴跌条件
   else if((totalDistance >= InpVolatileDistance && TotalAdditionCount <= InpVolatileAdditionCount) ||
           totalLoss <= InpMaxLossForHedge ||
           totalVolume >= InpMinTotalVolume)
   {
      ActivateHedge("暴涨暴跌或总持仓量过大");
   }
}

void ActivateHedge(string reason)
{
   IsHedgeActive = true;
   HedgeStartPrice = LastAdditionPrice;  // 使用最近加仓价格作为对冲起始价格
   Print("激活对冲策略。原因：", reason, "，起始价格（最近加仓价格）：", DoubleToString(HedgeStartPrice, _Digits));
}

void ManageHedgePositions()
{
   if(!IsHedgeActive)
      return;

   // 更新对冲秒级时间框架数据
   UpdateHedgeSecondTimeframeData();

   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                         : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   // 找出最近的加仓订单价格
   double latestAdditionPrice = LastAdditionPrice;
   ulong latestTicket = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_BUY) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_SELL))
         {
            latestAdditionPrice = positionInfo.PriceOpen();
            latestTicket = positionInfo.Ticket();
            break; // 找到最后一笔，退出循环
         }
      }
   }
   
   double priceDifference = (CurrentDirection == TRADE_DIRECTION_BUY)
                            ? currentPrice - latestAdditionPrice
                            : latestAdditionPrice - currentPrice;

   // 减少日志输出频率
   datetime currentTime = TimeCurrent();
   static datetime lastHedgeLogTime = 0;
   if(currentTime - lastHedgeLogTime >= 60) // 每60秒输出一次
   {
      Print("管理对冲仓位 - 当前价格: ", DoubleToString(currentPrice, _Digits), 
            ", 最近加仓价格: ", DoubleToString(latestAdditionPrice, _Digits),
            ", 价格差: ", DoubleToString(priceDifference, _Digits), 
            ", 反弹距离: ", DoubleToString(InpHedgeReboundDistance, _Digits),
            ", 秒级条件: ", CheckHedgeSecondTimeframeCondition() ? "满足" : "不满足");
      lastHedgeLogTime = currentTime;
   }

   if(priceDifference >= InpHedgeReboundDistance)
   {
      // 增加秒级时间框架条件判断
      bool secondTimeframeCondition = CheckHedgeSecondTimeframeCondition();
      
      if(secondTimeframeCondition)
      {
         Print("达到反弹距离和秒级条件，执行对冲");
         Print("对冲秒级信息 - 周期开始时间: ", TimeToString(hedgeSecondData.time),
               ", 开盘价: ", DoubleToString(hedgeSecondData.openPrice, _Digits),
               ", 当前价: ", DoubleToString(hedgeSecondData.currentPrice, _Digits),
               ", 价差: ", DoubleToString(hedgeSecondData.currentPrice - hedgeSecondData.openPrice, _Digits));
         ExecuteHedge();
      }
      else
      {
         Print("达到反弹距离但不满足秒级条件，暂不执行对冲");
      }
   }
}

void ExecuteHedge()
{
   double totalVolume = GetTotalPositionVolume();
   
   while(totalVolume >= InpMinTotalVolume)
   {
      // 找到最近加仓订单的价格
      double hedgePrice = 0;
      ulong hedgeTicket = 0;
      double hedgeProfit = 0;
      
      // 先找出将要对冲的最近建仓订单
      for(int i = PositionsTotal() - 1; i >= 0; i--)
      {
         if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
         {
            if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_BUY) ||
               (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_SELL))
            {
               hedgeTicket = positionInfo.Ticket();
               hedgeProfit = positionInfo.Profit();
               hedgePrice = positionInfo.PriceOpen();
               break;
            }
         }
      }
      
      if(hedgeTicket == 0)
      {
         Print("未找到合适的对冲订单进行平仓");
         break;
      }
      
      // 检查当前价格与这个订单的开仓价格之间是否满足反弹条件
      double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                           ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                           : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      
      double priceDifference = (CurrentDirection == TRADE_DIRECTION_BUY)
                              ? currentPrice - hedgePrice
                              : hedgePrice - currentPrice;
      
      Print("检查对冲订单反弹条件 - 订单号: ", hedgeTicket, 
            ", 开仓价格: ", DoubleToString(hedgePrice, _Digits),
            ", 当前价格: ", DoubleToString(currentPrice, _Digits),
            ", 价格差: ", DoubleToString(priceDifference, _Digits),
            ", 反弹距离要求: ", DoubleToString(InpHedgeReboundDistance, _Digits));
      
      // 如果这个订单不满足反弹条件，不对冲，直接退出
      if(priceDifference < InpHedgeReboundDistance)
      {
         Print("对冲订单不满足反弹条件，停止对冲");
         break;
      }
      
      // 执行对冲平仓
      Print("尝试平仓对冲订单 - 订单号: ", hedgeTicket, ", 预期利润: ", DoubleToString(hedgeProfit, 2));
      
      if(trade.PositionClose(hedgeTicket))
      {
         double actualProfit = hedgeProfit;
         Print("对冲平仓成功。订单号：", hedgeTicket, "，实际利润：", DoubleToString(actualProfit, 2));
         
         // 累加对冲利润
         AccumulatedHedgeProfit += actualProfit;
         Print("累计可用利润: ", DoubleToString(AccumulatedHedgeProfit, 2));
         
         // 使用累计对冲利润平掉亏损订单
         CloseLosingPositionsInOrderWithAccumulatedProfit();
         
         // 更新总持仓量
         totalVolume = GetTotalPositionVolume();
         Print("当前总持仓量: ", DoubleToString(totalVolume, 2));
      }
      else
      {
         Print("对冲平仓失败。错误代码：", GetLastError(), ", 描述: ", GetErrorDescription(GetLastError()));
         break; // 如果平仓失败，退出循环
      }
   }

   // 激活对冲后的止盈计算模式
   UseHedgeTakeProfit = true;
   LastHedgeTime = TimeCurrent();
   
   // 更新对冲后的止盈价格
   UpdateTakeProfitAfterHedge();
   
   // 重要修改：重新计算下一次加仓价格，确保不必等待创新低
   RecalculateNextAdditionPriceAfterHedge();

   // 重置对冲状态
   IsHedgeActive = false;
   HedgeStartPrice = 0;
   Print("对冲策略执行完毕，重置对冲状态");

   // 更新总距离和加仓次数的统计
   UpdateTradingStats();

   // 设置标志以在下一个 tick 重新检查对冲条件
   NeedRecheckHedge = true;
   
   // 在函数末尾调用ResetHedgeState
   ResetHedgeState();
}

// 新增函数：对冲后重新计算下一次加仓价格
void RecalculateNextAdditionPriceAfterHedge()
{
   // 计算当前总持仓和总亏损
   double totalLots = CalculateTotalLots();
   double totalLoss = CalculateTotalLoss();
   
   // 找到当前最新的加仓订单价格，更新LastAdditionPrice
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_BUY) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_SELL))
         {
            LastAdditionPrice = positionInfo.PriceOpen();
            break;
         }
      }
   }
   
   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) 
                         : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // 若AdditionCount小于4，使用固定间隔计算加仓位置
   if(AdditionCount < 4)
   {
      double additionInterval = 0;
      
      switch(AdditionCount)
      {
         case 0: additionInterval = 第一次加仓间隔 * InpDistanceMultiplier; break;
         case 1: additionInterval = 第二次加仓间隔 * InpDistanceMultiplier; break;
         case 2: additionInterval = 第三次加仓间隔 * InpDistanceMultiplier; break;
         case 3: additionInterval = 第四次加仓间隔 * InpDistanceMultiplier; break;
      }
      
      // 应用对冲后加仓距离倍数，扩大加仓间隔
      additionInterval *= InpHedgeAdditionMultiplier;
      
      // 根据交易方向计算下一次加仓价格
      if(CurrentDirection == TRADE_DIRECTION_BUY)
      {
         NextAdditionPrice = LastAdditionPrice - additionInterval;
      }
      else
      {
         NextAdditionPrice = LastAdditionPrice + additionInterval;
      }
      
      Print("对冲后重新计算下一次加仓价格(固定间隔) - 原始间隔: ", 
            DoubleToString(additionInterval / InpHedgeAdditionMultiplier, _Digits),
            ", 调整后间隔: ", DoubleToString(additionInterval, _Digits),
            ", 下次加仓价格: ", DoubleToString(NextAdditionPrice, _Digits));
   }
   else
   {
      // 使用保本距离计算逻辑，但修改计算过程以应用倍数
      double originalNextPrice = CalculateNextAdditionPrice(totalLots, totalLoss);
      
      // 计算与当前LastAdditionPrice的距离
      double distance = MathAbs(originalNextPrice - LastAdditionPrice);
      
      // 应用倍数扩大距离
      distance *= InpHedgeAdditionMultiplier;
      
      // 根据交易方向重新计算下一次加仓价格
      if(CurrentDirection == TRADE_DIRECTION_BUY)
      {
         NextAdditionPrice = LastAdditionPrice - distance;
      }
      else
      {
         NextAdditionPrice = LastAdditionPrice + distance;
      }
      
      Print("对冲后重新计算下一次加仓价格(保本距离) - 原始价格: ", 
            DoubleToString(originalNextPrice, _Digits),
            ", 原始距离: ", DoubleToString(MathAbs(originalNextPrice - LastAdditionPrice), _Digits),
            ", 调整后距离: ", DoubleToString(distance, _Digits),
            ", 下次加仓价格: ", DoubleToString(NextAdditionPrice, _Digits));
   }
}

// 新函数：使用累计利润平掉亏损订单
void CloseLosingPositionsInOrderWithAccumulatedProfit()
{
   Print("开始平掉亏损订单 - 累计可用利润: ", DoubleToString(AccumulatedHedgeProfit, 2));
   
   // 如果累计利润小于等于0，直接返回
   if(AccumulatedHedgeProfit <= 0)
   {
      Print("累计利润不足，无法对冲亏损订单");
      return;
   }
   
   LosingPosition losingPositions[];
   
   // 收集所有亏损订单的信息
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         double positionLoss = positionInfo.Profit();
         if(positionLoss < 0)
         {
            int size = ArraySize(losingPositions);
            ArrayResize(losingPositions, size + 1);
            losingPositions[size].ticket = positionInfo.Ticket();
            losingPositions[size].loss = positionLoss;
            losingPositions[size].orderNumber = GetOrderNumber(positionInfo.Comment());
         }
      }
   }
   
   // 手动排序亏损订单
   int n = ArraySize(losingPositions);
   for(int i = 0; i < n - 1; i++)
   {
      for(int j = 0; j < n - i - 1; j++)
      {
         if(CompareLosingPositions(losingPositions[j], losingPositions[j + 1]) > 0)
         {
            LosingPosition temp = losingPositions[j];
            losingPositions[j] = losingPositions[j + 1];
            losingPositions[j + 1] = temp;
         }
      }
   }
   
   // 从小到大平掉亏损订单
   for(int i = 0; i < ArraySize(losingPositions); i++)
   {
      if(MathAbs(losingPositions[i].loss) <= AccumulatedHedgeProfit)
      {
         if(trade.PositionClose(losingPositions[i].ticket))
         {
            Print("成功平掉亏损订单。订单号：", losingPositions[i].ticket, "，亏损：", DoubleToString(losingPositions[i].loss, 2));
            AccumulatedHedgeProfit += losingPositions[i].loss;  // 减少累计利润（因为loss是负值，所以用加法）
            Print("剩余累计可用利润: ", DoubleToString(AccumulatedHedgeProfit, 2));
         }
         else
         {
            Print("平掉亏损订单失败。错误代码：", GetLastError(), ", 描述: ", GetErrorDescription(GetLastError()));
         }
      }
      else
      {
         Print("亏损订单亏损额超过累计可用利润。订单号：", losingPositions[i].ticket, 
               "，亏损：", DoubleToString(losingPositions[i].loss, 2), 
               "，可用利润：", DoubleToString(AccumulatedHedgeProfit, 2));
         break; // 可用利润不足，停止平仓
      }
   }
   
   Print("平掉亏损订单完成 - 剩余累计可用利润: ", DoubleToString(AccumulatedHedgeProfit, 2));
}

double GetTotalPositionVolume()
{
   double totalVolume = 0;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         totalVolume += positionInfo.Volume();
      }
   }
   return totalVolume;
}

void CloseLosingPositionsInOrder(double availableProfit)
{
   Print("开始平掉亏损订单 - 可用利润: ", DoubleToString(availableProfit, 2));
   
   LosingPosition losingPositions[];
   
   // 收集所有亏损订单的信息
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         double positionLoss = positionInfo.Profit();
         if(positionLoss < 0)
         {
            int size = ArraySize(losingPositions);
            ArrayResize(losingPositions, size + 1);
            losingPositions[size].ticket = positionInfo.Ticket();
            losingPositions[size].loss = positionLoss;
            losingPositions[size].orderNumber = GetOrderNumber(positionInfo.Comment());
         }
      }
   }
   
   // 手动排序亏损订单
   int n = ArraySize(losingPositions);
   for(int i = 0; i < n - 1; i++)
   {
      for(int j = 0; j < n - i - 1; j++)
      {
         if(CompareLosingPositions(losingPositions[j], losingPositions[j + 1]) > 0)
         {
            LosingPosition temp = losingPositions[j];
            losingPositions[j] = losingPositions[j + 1];
            losingPositions[j + 1] =
            temp;
         }
      }
   }
   
   // 从小到大平掉亏损订单
   for(int i = 0; i < ArraySize(losingPositions); i++)
   {
      if(MathAbs(losingPositions[i].loss) <= availableProfit)
      {
         if(trade.PositionClose(losingPositions[i].ticket))
         {
            Print("成功平掉亏损订单。订单号：", losingPositions[i].ticket, "，亏损：", DoubleToString(losingPositions[i].loss, 2));
            availableProfit += losingPositions[i].loss;
         }
         else
         {
            Print("平掉亏损订单失败。错误代码：", GetLastError(), ", 描述: ", GetErrorDescription(GetLastError()));
         }
      }
      else
      {
         break; // 可用利润不足，停止平仓
      }
   }
   
   Print("平掉亏损订单完成 - 剩余可用利润: ", DoubleToString(availableProfit, 2));
}

int GetOrderNumber(string comment)
{
   // 假设订单注释格式为 "加仓 #X" 或 "初始建仓"
   if(StringFind(comment, "初始建仓") >= 0)
      return 0;
   
   int pos = StringFind(comment, "#");
   if(pos >= 0)
   {
      string numberStr = StringSubstr(comment, pos + 1);
      return (int)StringToInteger(numberStr);
   }
   
   return 999; // 如果无法解析，返回一个大数，确保它排在最后
}

int CompareLosingPositions(const LosingPosition &a, const LosingPosition &b)
{
   return a.orderNumber - b.orderNumber;
}

void UpdateTradingStats()
{
   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                         : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   double minOrderPrice = 0;
   int positionCount = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         positionCount++;
         if(minOrderPrice == 0 || positionInfo.PriceOpen() < minOrderPrice)
         {
            minOrderPrice = positionInfo.PriceOpen();
         }
      }
   }
   
   if(minOrderPrice != 0)
   {
      TotalDistance = MathAbs(currentPrice - minOrderPrice);
   }
   else
   {
      TotalDistance = 0;
   }
   
   AdditionCount = MathMax(0, positionCount - 1); // 减去初始订单
   TotalAdditionCount = AdditionCount; // 确保TotalAdditionCount同步更新
   
   Print("更新交易统计 - 总距离: ", DoubleToString(TotalDistance, 2), 
         ", 加仓次数: ", AdditionCount);
}

void CloseLosingPositions(double availableProfit)
{
   Print("开始平掉亏损订单 - 可用利润: ", DoubleToString(availableProfit, 2));
   
   for(int i = 0; i < ArraySize(OriginalOrderPrices); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         double positionLoss = positionInfo.Profit();
         if(positionLoss < 0 && MathAbs(positionLoss) <= availableProfit)
         {
            Print("尝试平仓亏损订单 - 订单号: ", positionInfo.Ticket(), ", 亏损: ", DoubleToString(positionLoss, 2));
            
            if(trade.PositionClose(positionInfo.Ticket()))
            {
               Print("成功平掉亏损订单。订单号：", positionInfo.Ticket(), "，亏损：", DoubleToString(positionLoss, 2));
               availableProfit += positionLoss;
            }
            else
            {
               Print("平掉亏损订单失败。错误代码：", GetLastError(), ", 描述: ", GetErrorDescription(GetLastError()));
            }
         }
      }
   }
   
   Print("平掉亏损订单完成 - 剩余可用利润: ", DoubleToString(availableProfit, 2));
}

void ManageReverseTrading()
{
   int reversePositionsCount = CountReversePositions();
   if(reversePositionsCount >= 5)
      return; // 如果已经达到最大加仓次数，不再进行加仓

   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                         : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   if(reversePositionsCount == 0)
   {
      // 检查是否需要开立新的反向交易
      double reversePrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? InitialOrderPrice - InpReverseTradeDistance * InpDistanceMultiplier 
                         : InitialOrderPrice + InpReverseTradeDistance * InpDistanceMultiplier;
      
      if((CurrentDirection == TRADE_DIRECTION_BUY && currentPrice <= reversePrice) ||
         (CurrentDirection == TRADE_DIRECTION_SELL && currentPrice >= reversePrice))
      {
         OpenReversePosition(0, currentPrice);
      }
   }
   else
   {
      // 检查是否需要进行反向交易的加仓
      double lastReversePrice = GetLastReversePositionPrice();
      double additionPrice = (CurrentDirection == TRADE_DIRECTION_BUY)
                          ? lastReversePrice + InpReverseAdditionDistance * InpDistanceMultiplier
                          : lastReversePrice - InpReverseAdditionDistance * InpDistanceMultiplier;

      if((CurrentDirection == TRADE_DIRECTION_BUY && currentPrice >= additionPrice) ||
         (CurrentDirection == TRADE_DIRECTION_SELL && currentPrice <= additionPrice))
      {
         OpenReversePosition(reversePositionsCount, currentPrice);
      }
   }
}

double GetLastReversePositionPrice()
{
   double lastPrice = 0;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_SELL) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_BUY))
         {
            lastPrice = positionInfo.PriceOpen();
            break;
         }
      }
   }
   return lastPrice;
}

void OpenReversePosition(int index, double price)
{
   ENUM_ORDER_TYPE reverseOrderType = (CurrentDirection == TRADE_DIRECTION_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
   
   double lotSize = InpReverseTradeVolume;
   string comment;
   
   int reversePositionsCount = CountReversePositions();
   if(reversePositionsCount == 0)
   {
      comment = "反向交易初始建仓";
   }
   else
   {
      comment = StringFormat("反向第%d次加仓", reversePositionsCount);
   }
   
   double tp = CalculateReverseTakeProfit(price, reversePositionsCount > 0);
   
   // 检查止盈价格是否有效
   double minDistance = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
   if(reverseOrderType == ORDER_TYPE_BUY)
   {
      if(tp - price < minDistance)
      {
         tp = price + minDistance;
      }
   }
   else
   {
      if(price - tp < minDistance)
      {
         tp = price - minDistance;
      }
   }
   
   // 使用修改后的止盈价格尝试开仓
   if(trade.PositionOpen(_Symbol, reverseOrderType, lotSize, price, 0, tp, comment))
   {
      ulong ticket = trade.ResultOrder();
      if(index >= ArraySize(ReverseTradeTickets))
         ArrayResize(ReverseTradeTickets, index + 1);
      ReverseTradeTickets[index] = ticket;
      
      Print("开立反向交易成功，订单号：", ticket, " 对应原始订单索引：", index, " 止盈：", tp, " ", comment);
      
      // 更新所有反向交易的止盈
      UpdateReverseTakeProfit(price);
   }
   else
   {
      int errorCode = GetLastError();
      Print("开立反向交易失败，错误代码：", errorCode, " 描述：", ErrorDescription(errorCode));
   }
}

int CountReversePositions()
{
   int count = 0;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_SELL) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_BUY))
         {
            count++;
         }
      }
   }
   return count;
}

void ManageReversePositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_SELL) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_BUY))
         {
            // 这是一个反向交易
            double openPrice = positionInfo.PriceOpen();
            double currentPrice = (positionInfo.PositionType() == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            
            if(MathAbs(currentPrice - openPrice) >= InpReverseAdditionDistance)
            {
               // 需要反向加仓
               OpenReverseAdditionPosition(positionInfo.PositionType());
            }
         }
      }
   }
}

void OpenReverseAdditionPosition(ENUM_POSITION_TYPE positionType)
{
   ENUM_ORDER_TYPE orderType = (positionType == POSITION_TYPE_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
   double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double volume = InpReverseTradeVolume;
   
   if(ReverseOrderCount >= 4)
   {
      volume *= 2; // 第四次加仓倍数
   }
   else if(ReverseOrderCount >= 5)
   {
      Print("已达到最大反向加仓次数，不再进行加仓");
      return;
   }
   
   double tp = (orderType == ORDER_TYPE_BUY) ? price + InpReverseAdditionTakeProfit : price - InpReverseAdditionTakeProfit;
   
   string comment = StringFormat("反向第%d次加仓", ReverseOrderCount);
   
   if(trade.PositionOpen(_Symbol, orderType, volume, price, 0, tp, comment))
   {
      ulong ticket = trade.ResultOrder();
      ReverseOrderCount++;
      Print("反向加仓成功，订单号：", ticket, " ", comment);
      
      // 更新所有反向交易的止盈
      UpdateReverseTakeProfit(price);
   }
   else
   {
      Print("反向加仓失败，错误代码：", GetLastError());
   }
}

void UpdateAllReverseTakeProfits(double latestEntryPrice)
{
   double newTakeProfit = CalculateReverseTakeProfit(latestEntryPrice, true);
   
   for(int i = 0; i < ArraySize(ReverseTradeTickets); i++)
   {
      if(ReverseTradeTickets[i] != 0 && PositionSelectByTicket(ReverseTradeTickets[i]))
      {
         if(trade.PositionModify(ReverseTradeTickets[i], 0, newTakeProfit))
         {
            Print("已更新反向交易 ", ReverseTradeTickets[i], " 的止盈到 ", newTakeProfit);
         }
         else
         {
            Print("更新反向交易止盈失败。错误代码：", GetLastError(), " 描述：", GetErrorDescription(GetLastError()));
         }
      }
   }
}

double CalculateReverseTakeProfit(double entryPrice, bool isAddition)
{
   double takeProfit;
   if (isAddition)
   {
      // 如果是加仓或者有多于一个反向订单，止盈为3美金
      takeProfit = (CurrentDirection == TRADE_DIRECTION_BUY) ? entryPrice - 3.0 * InpDistanceMultiplier : entryPrice + 3.0 * InpDistanceMultiplier;
   }
   else
   {
      // 如果只有一个反向订单（初始建仓），止盈为1.5美金
      takeProfit = (CurrentDirection == TRADE_DIRECTION_BUY) ? entryPrice - 1.5 * InpDistanceMultiplier : entryPrice + 1.5 * InpDistanceMultiplier;
   }
   
   // 确保止盈价格符合经纪商的最小距离要求
   double minDistance = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      takeProfit = MathMin(takeProfit, entryPrice - minDistance);
   }
   else
   {
      takeProfit = MathMax(takeProfit, entryPrice + minDistance);
   }
   
   return NormalizeDouble(takeProfit, _Digits);
}

void CheckAndUpdateTakeProfit()
{
   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   if(MathAbs(currentPrice - lastUpdatePrice) >= UPDATE_THRESHOLD)
   {
      UpdateProtectionLevelAndTakeProfit();
      lastUpdatePrice = currentPrice;
   }
}

void UpdateTakeProfitAfterHedge()
{
   // 收集所有持仓的信息
   double totalVolume = 0.0;
   double lastPositionPrice = 0.0;
   ulong lastPositionTicket = 0;
   
   // 首先找到最后一笔持仓的价格
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         // 排除反向交易
         bool isReversePosition = false;
         for(int j = 0; j < ArraySize(ReverseTradeTickets); j++)
         {
            if(positionInfo.Ticket() == ReverseTradeTickets[j])
            {
               isReversePosition = true;
               break;
            }
         }
         if(isReversePosition) continue;
         
         // 更新最后一笔持仓的价格和单号
         lastPositionPrice = positionInfo.PriceOpen();
         lastPositionTicket = positionInfo.Ticket();
         totalVolume += positionInfo.Volume();
      }
   }
   
   // 如果没有持仓，直接返回
   if(totalVolume <= 0 || lastPositionPrice == 0) 
   {
      Print("对冲后更新止盈 - 未找到有效持仓");
      return;
   }
   
   // 计算固定的总亏损额（基于入场价格的差异）
   double fixedTotalLoss = 0.0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         // 排除反向交易
         bool isReversePosition = false;
         for(int j = 0; j < ArraySize(ReverseTradeTickets); j++)
         {
            if(positionInfo.Ticket() == ReverseTradeTickets[j])
            {
               isReversePosition = true;
               break;
            }
         }
         if(isReversePosition) continue;
         
         double volume = positionInfo.Volume();
         double openPrice = positionInfo.PriceOpen();
         
         // 计算每笔持仓相对于最后一笔持仓的价格差异造成的亏损
         if(CurrentDirection == TRADE_DIRECTION_BUY)
         {
            // 买入方向，价格越低亏损越大
            double priceDiff = lastPositionPrice - openPrice; // 正值表示这笔持仓价格比最后一笔低
            fixedTotalLoss -= priceDiff * volume * 100; // 乘以100是因为黄金每1美元价格变动约对应100美元盈亏
         }
         else
         {
            // 卖出方向，价格越高亏损越大
            double priceDiff = openPrice - lastPositionPrice; // 正值表示这笔持仓价格比最后一笔高
            fixedTotalLoss -= priceDiff * volume * 100;
         }
      }
   }
   
   // 基于固定总亏损计算保本距离
   double breakEvenDistance = MathAbs(fixedTotalLoss) / (totalVolume * 100.0);
   
   // 根据交易方向计算保本价格
   double breakEvenPrice = 0;
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      breakEvenPrice = lastPositionPrice + breakEvenDistance;
   }
   else
   {
      breakEvenPrice = lastPositionPrice - breakEvenDistance;
   }
   
   // 加上额外的利润
   double profitMargin = InpHedgeProfitMargin * InpDistanceMultiplier; // 假设您已添加这个参数
   double newTakeProfit = 0;
   
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      newTakeProfit = breakEvenPrice + profitMargin;
   }
   else
   {
      newTakeProfit = breakEvenPrice - profitMargin;
   }
   
   // 将价格规范化
   breakEvenPrice = NormalizeDouble(breakEvenPrice, _Digits);
   newTakeProfit = NormalizeDouble(newTakeProfit, _Digits);
   
   // 存储计算结果以避免重复更新
   static double savedTakeProfit = 0;
   
   // 只有在第一次计算或止盈价格有显著变化时才更新
   if(savedTakeProfit == 0 || MathAbs(newTakeProfit - savedTakeProfit) > _Point * 10)
   {
      savedTakeProfit = newTakeProfit;
      
      // 记录调试信息
      Print("对冲后更新止盈(固定计算) - 总持仓: ", DoubleToString(totalVolume, 2), 
            ", 固定总亏损: ", DoubleToString(fixedTotalLoss, 2),
            ", 最后持仓价格: ", DoubleToString(lastPositionPrice, _Digits),
            ", 保本距离: ", DoubleToString(breakEvenDistance, _Digits),
            ", 保本价格: ", DoubleToString(breakEvenPrice, _Digits),
            ", 新止盈价格: ", DoubleToString(newTakeProfit, _Digits));
      
      // 更新所有持仓的止盈价格
      for(int i = 0; i < PositionsTotal(); i++)
      {
         if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
         {
            // 排除反向交易
            bool isReversePosition = false;
            for(int j = 0; j < ArraySize(ReverseTradeTickets); j++)
            {
               if(positionInfo.Ticket() == ReverseTradeTickets[j])
               {
                  isReversePosition = true;
                  break;
               }
            }
            if(isReversePosition) continue;
            
            // 检查新的止盈价格是否有效
            if(!IsValidTakeProfit(positionInfo.PositionType(), newTakeProfit))
            {
               Print("对冲后无效的止盈价格: ", newTakeProfit, " 对于持仓 ", positionInfo.Ticket());
               continue;
            }
            
            // 只有当新的止盈价格与当前止盈价格不同时才更新
            if(MathAbs(newTakeProfit - positionInfo.TakeProfit()) > _Point)
            {
               if(trade.PositionModify(positionInfo.Ticket(), 0, newTakeProfit))
               {
                  Print("对冲后已更新持仓 ", positionInfo.Ticket(), " 的止盈到 ", newTakeProfit);
               }
               else
               {
                  int errorCode = GetLastError();
                  Print("对冲后更新止盈失败。错误代码：", errorCode, " 描述：", GetErrorDescription(errorCode));
               }
            }
         }
      }
   }
}

bool CanAddPosition()
{
   if(!InpEnableCandleTimeframe)
   {
      return true; // 如果未启用蜡烛图时间框架功能，始终返回 true
   }

   datetime currentCandleTime = iTime(_Symbol, InpCandleTimeframe, 0);
   
   if(currentCandleTime != lastAdditionTime)
   {
      additionsThisCandle = 0; // 重置计数器
   }
   
   if(additionsThisCandle >= InpMaxAdditionsPerCandle)
   {
      return false; // 已达到这根蜡烛图的最大加仓次数
   }
   
   return true;
}

bool CheckCandleCondition()
{
   if(!InpEnableCandleTimeframe)
   {
      return true; // 如果未启用蜡烛图时间框架功能，始终返回 true
   }

   MqlRates rates[];
   if(CopyRates(_Symbol, InpCandleTimeframe, 1, 1, rates) != 1)
   {
      Print("无法复制价格数据");
      return false;
   }
   
   bool isValidCandle = false;
   
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      isValidCandle = (rates[0].close > rates[0].open); // 阳线
   }
   else if(CurrentDirection == TRADE_DIRECTION_SELL)
   {
      isValidCandle = (rates[0].close < rates[0].open); // 阴线
   }
   
   return isValidCandle;
}

void UpdateProtectionLevelAndTakeProfit()
{
   double totalDistance = MathAbs(LastAdditionPrice - InitialOrderPrice);
   
   // 只有当总距离变化超过阈值时才重新计算保护级别
   if(MathAbs(totalDistance - lastTotalDistance) >= DISTANCE_THRESHOLD * InpDistanceMultiplier)
   {
      double oldProtectionLevel = CurrentProtectionLevel;
      
      if(totalDistance <= 7.5 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 2.5 * InpDistanceMultiplier;
         DynamicTakeProfit = 前5笔最终止盈 * InpDistanceMultiplier;
      }
      else if(totalDistance <= 15 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 保本级别_0_15 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_0_15 * InpDistanceMultiplier;
      }
      else if(totalDistance <= 20 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 保本级别_15_20 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_15_20 * InpDistanceMultiplier;
      }
      else if(totalDistance <= 25 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 保本级别_20_25 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_20_25 * InpDistanceMultiplier;
      }
      else if(totalDistance <= 30 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 保本级别_25_30 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_25_30 * InpDistanceMultiplier;
      }
      else if(totalDistance <= 35 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 保本级别_30_35 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_30_35 * InpDistanceMultiplier;
      }
      else if(totalDistance <= 40 * InpDistanceMultiplier)
      {
         CurrentProtectionLevel = 保本级别_35_40 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_35_40 * InpDistanceMultiplier;
      }
      else
      {
         CurrentProtectionLevel = 保本级别_40_45 * InpDistanceMultiplier;
         DynamicTakeProfit = 止盈_40_45 * InpDistanceMultiplier;
      }
      
      // 如果保护级别发生变化，相应调整动态止盈
      if(CurrentProtectionLevel != oldProtectionLevel)
      {
         double protectionLevelDifference = CurrentProtectionLevel - oldProtectionLevel;
         DynamicTakeProfit = MathMax(DynamicTakeProfit + protectionLevelDifference, CurrentProtectionLevel);
      }
      
      lastTotalDistance = totalDistance;
      
      DebugPrint("保护级别已调整。原值：" + DoubleToString(oldProtectionLevel, 2) + " 新值：" + DoubleToString(CurrentProtectionLevel, 2));
   }
   
   // 确保动态止盈始终高于保护级别
   DynamicTakeProfit = MathMax(DynamicTakeProfit, CurrentProtectionLevel + InpAdditionalProfit * InpDistanceMultiplier);
   
   DebugPrint("动态止盈已相应调整。新值：" + DoubleToString(DynamicTakeProfit, 2) + " 美元");
   
   // 更新止损水平（如果需要）
   if(cachedStopLevel == 0)
   {
      cachedStopLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
   }
}

void DebugPrint(string message)
{
   if(InpDebugMode)
   {
      Print(message);
   }
}

void UpdateAllPositionsTakeProfit()
{
   // 如果处于对冲后的止盈模式，不使用此函数更新止盈
   if(UseHedgeTakeProfit)
   {
      // 如果需要，可以定期重新计算对冲后的止盈
      if(TimeCurrent() - LastHedgeTime >= 60) // 每分钟检查一次
      {
         UpdateTakeProfitAfterHedge();
         LastHedgeTime = TimeCurrent();
      }
      return;
   }
   
   // 先更新保护级别和动态止盈价格
   UpdateProtectionLevelAndTakeProfit();
   
   static datetime lastUpdateTime = 0;
   datetime currentTime = TimeCurrent();

   if(currentTime - lastUpdateTime < 0.1) // 每0.1秒钟最多更新一次（之前30，后面改为5，现在改为0.1）
      return;

   lastUpdateTime = currentTime;

   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         // 检查是否为反向交易
         bool isReversePosition = false;
         for(int j = 0; j < ArraySize(ReverseTradeTickets); j++)
         {
            if(positionInfo.Ticket() == ReverseTradeTickets[j])
            {
               isReversePosition = true;
               break;
            }
         }
         
         // 如果是反向交易，跳过
         if(isReversePosition)
            continue;

         double newTakeProfit;
         if(positionInfo.PositionType() == POSITION_TYPE_BUY)
         {
            newTakeProfit = NormalizeDouble(LastAdditionPrice + DynamicTakeProfit, _Digits);
         }
         else
         {
            newTakeProfit = NormalizeDouble(LastAdditionPrice - DynamicTakeProfit, _Digits);
         }
         
         // 检查新的止盈价格是否有效
         if(!IsValidTakeProfit(positionInfo.PositionType(), newTakeProfit))
         {
            Print("无效的止盈价格: ", newTakeProfit, " 对于持仓 ", positionInfo.Ticket());
            continue;
         }
         
         // 如果新的止盈价格与当前止盈价格相同，则跳过更新
         if(MathAbs(newTakeProfit - positionInfo.TakeProfit()) < _Point)
         {
            continue;
         }
         
         // 尝试更新止盈
         if(trade.PositionModify(positionInfo.Ticket(), 0, newTakeProfit))
         {
            Print("已更新持仓 ", positionInfo.Ticket(), " 的止盈到 ", newTakeProfit);
         }
         else
         {
            int errorCode = GetLastError();
            Print("更新止盈失败。错误代码：", errorCode, " 描述：", GetErrorDescription(errorCode));
         }
      }
   }
}

void UpdateReverseTakeProfit(double latestPrice)
{
   int reversePositionsCount = CountReversePositions();
   if(reversePositionsCount == 0)
      return;

   double newTakeProfit = CalculateReverseTakeProfit(latestPrice, reversePositionsCount > 1);
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if((CurrentDirection == TRADE_DIRECTION_BUY && positionInfo.PositionType() == POSITION_TYPE_SELL) ||
            (CurrentDirection == TRADE_DIRECTION_SELL && positionInfo.PositionType() == POSITION_TYPE_BUY))
         {
            if(MathAbs(positionInfo.TakeProfit() - newTakeProfit) > _Point)
            {
               if(trade.PositionModify(positionInfo.Ticket(), 0, newTakeProfit))
               {
                  Print("已更新反向交易 ", positionInfo.Ticket(), " 的止盈到 ", newTakeProfit);
               }
               else
               {
                  Print("更新反向交易止盈失败。错误代码：", GetLastError(), " 描述：", GetErrorDescription(GetLastError()));
               }
            }
         }
      }
   }
}

// 新增函数：检查止盈价格是否有效
bool IsValidTakeProfit(ENUM_POSITION_TYPE positionType, double takeProfit)
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   int stopLevel = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   
   if(positionType == POSITION_TYPE_BUY)
   {
      return (takeProfit > ask + stopLevel * _Point);
   }
   else
   {
      return (takeProfit < bid - stopLevel * _Point);
   }
}

void AddPosition(double lotSize, int additionNumber)
{
   ENUM_ORDER_TYPE orderType = (CurrentDirection == TRADE_DIRECTION_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

   // 检查趋势过滤器
   if(!CheckTrendFilter(orderType))
   {
      Print("趋势过滤器阻止加仓操作 #", additionNumber);
      return;
   }

   double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);

   // 新增：灵活手数管理模式
   double finalLotSize = lotSize;
   if(InpUseLotMultiplier && AdditionCount > 0)
   {
      // 获取最后一笔已开仓订单的手数
      double lastLotSize = GetLastPositionLotSize();
      if(lastLotSize > 0)
      {
         // 使用乘数模式计算新手数
         double calculatedLot = lastLotSize * InpLotMultiplier;
         // 应用最大手数限制
         finalLotSize = MathMin(calculatedLot, InpMaxLot);
         finalLotSize = NormalizeDouble(finalLotSize, 2);

         Print("手数乘数模式 - 上次手数: ", DoubleToString(lastLotSize, 2),
               ", 乘数: ", DoubleToString(InpLotMultiplier, 2),
               ", 计算手数: ", DoubleToString(calculatedLot, 2),
               ", 最终手数: ", DoubleToString(finalLotSize, 2));
      }
   }
   else if(!InpUseLotMultiplier)
   {
      // 使用原代码的固定手数数组模式
      finalLotSize = lotSize;
      Print("固定手数数组模式 - 使用预设手数: ", DoubleToString(finalLotSize, 2));
   }

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = finalLotSize;
   request.type = orderType;
   request.price = price;
   request.deviation = InpMaxDeviation;
   request.comment = StringFormat("加仓 #%d", additionNumber);
   request.type_filling = ORDER_FILLING_IOC;
   request.magic = MagicNumber;
   
   if(!OrderSend(request, result))
   {
      int error = GetLastError();
      Print("加仓订单发送失败，错误代码：", error, "，描述：", GetErrorDescription(error));
      return;
   }
   
   if(result.retcode == TRADE_RETCODE_DONE)
   {
      AdditionCount++;
      TotalAdditionCount++;
      LastAdditionPrice = price;  // 更新最近加仓价格
      
      // 记录新的加仓价格到OriginalOrderPrices数组
      int size = ArraySize(OriginalOrderPrices);
      ArrayResize(OriginalOrderPrices, size + 1);
      OriginalOrderPrices[size] = price;
      
      lastAdditionTime = iTime(_Symbol, InpCandleTimeframe, 0);
      additionsThisCandle++;
      
      string additionalInfo = "";
      if(InpEnableSecondTimeframe)
      {
         additionalInfo = StringFormat(", 秒级周期内加仓次数: %d, 开盘价: %f, 收盘价: %f", 
                                       additionsThisSecond, 
                                       NormalizeDouble(currentSecondOpen, _Digits),
                                       NormalizeDouble(currentSecondClose, _Digits));
      }
      
      Print("加仓 #", additionNumber, " 已开立。单号: ", result.order,
            ", 交易量: ", finalLotSize, ", 价格: ", LastAdditionPrice,
            ", 当前方向: ", EnumToString(CurrentDirection), additionalInfo);
      
      // 更新保护级别和止盈
      UpdateProtectionLevelAndTakeProfit();
      
      if(AdditionCount >= 5)
      {
         NextAdditionPrice = CalculateNextAdditionPrice(CalculateTotalLots(), CalculateTotalLoss());
         Print("下一次加仓价格设置为: ", NextAdditionPrice);
      }
   }
   else
   {
      Print("加仓订单执行失败，返回代码：", result.retcode, "，描述：", GetErrorDescription(result.retcode));
   }
}

double CalculateTotalLots()
{
   double totalLots = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         totalLots += positionInfo.Volume();
      }
   }
   return totalLots;
}

double CalculateTotalLoss()
{
   double totalLoss = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         totalLoss += positionInfo.Profit();
      }
   }
   return totalLoss;
}

double CalculateNextAdditionPrice(double totalLots, double totalLoss, bool returnOriginal = false)
{
   int index = AdditionCount - 3;  // 计算下一次加仓的手数
   double nextLotSize = (index < ArraySize(AdditionLotSizes)) ? AdditionLotSizes[index] : AdditionLotSizes[ArraySize(AdditionLotSizes) - 1];
   nextLotSize *= InpOrderMultiplier;
   
   double adjustedProtectionLevel = CurrentProtectionLevel;
   double protectionAmount = adjustedProtectionLevel * (totalLots + nextLotSize) * 100;
   
   while(protectionAmount <= MathAbs(totalLoss))
   {
      adjustedProtectionLevel += 0.2;
      protectionAmount = adjustedProtectionLevel * (totalLots + nextLotSize) * 100;
   }
   
   if(adjustedProtectionLevel > CurrentProtectionLevel)
   {
      Print("警告：保护级别已自适应调整。原值：", CurrentProtectionLevel, " 新值：", adjustedProtectionLevel);
      CurrentProtectionLevel = adjustedProtectionLevel;
      CurrentTakeProfit += (adjustedProtectionLevel - CurrentProtectionLevel);
   }
   
   double x = MathAbs((-totalLoss - CurrentProtectionLevel * (totalLots + nextLotSize) * 100) / (totalLots * 100));
   x = NormalizeDouble(x, _Digits);
   
   double nextPrice;
   if(CurrentDirection == TRADE_DIRECTION_BUY)
   {
      nextPrice = LastAdditionPrice - x;
   }
   else // TRADE_DIRECTION_SELL
   {
      nextPrice = LastAdditionPrice + x;
   }
   nextPrice = NormalizeDouble(nextPrice, _Digits);
   
   Print("计算下一个加仓价格 - 总持仓: ", totalLots, ", 总亏损: ", totalLoss, 
         ", 下一手数: ", nextLotSize, ", 计算距离: ", x, 
         ", 上次价格: ", LastAdditionPrice, ", 下次价格: ", nextPrice,
         ", 交易方向: ", (CurrentDirection == TRADE_DIRECTION_BUY) ? "买入" : "卖出",
         ", 调整后保护级别: ", CurrentProtectionLevel,
         ", 调整后止盈: ", CurrentTakeProfit);
   
   return nextPrice;
}

void CheckForTakeProfit()
{
   // 如果处于对冲后的止盈模式，不使用动态止盈逻辑
   if(UseHedgeTakeProfit)
   {
      return;
   }

   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double priceDifference = (CurrentDirection == TRADE_DIRECTION_BUY) ? currentPrice - LastAdditionPrice : LastAdditionPrice - currentPrice;
   
   if(priceDifference >= DynamicTakeProfit)
   {
      Print("达到动态止盈条件。价格差：", priceDifference, " 动态止盈：", DynamicTakeProfit, " 美元");
      CloseAllPositions();
   }
}

void CloseAllPositions()
{
   bool allClosed = true;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(positionInfo.SelectByIndex(i) && positionInfo.Magic() == MagicNumber)
      {
         if(!trade.PositionClose(positionInfo.Ticket()))
         {
            Print("无法平仓订单 ", positionInfo.Ticket(), ". 错误: ", GetLastError());
            allClosed = false;
         }
      }
   }
   
   if(allClosed)
   {
      UseHedgeTakeProfit = false; // 重置对冲止盈标志
      
      // 重置其他对冲相关变量
      static bool hedgeTakeProfitCalculated = false;
      static double fixedHedgeTakeProfit = 0;
      hedgeTakeProfitCalculated = false;
      fixedHedgeTakeProfit = 0;
      
      ArrayFree(OriginalOrderPrices);
      ArrayFree(ReverseTradeTickets);
      PrepareForNewTradeCycle();
   }
   else
   {
      Print("警告：部分订单未能平仓");
   }
}

void ResetTrade()
{
   BuyTicket = 0;
   SellTicket = 0;
   AdditionCount = 0;
   InitialOrderPrice = 0;
   LastAdditionPrice = 0;
   NextAdditionPrice = 0;
   IsInitialTradeOpen = false;
   IsMartingaleActive = false;
   ContinueExistingTrade = false;
   CurrentProtectionLevel = 2.5;
   CurrentTakeProfit = 前5笔最终止盈;
   isInitialOrderPlaced = false;
   lastAdditionTime = 0;
   additionsThisCandle = 0;
   initialOrderTime = 0;
   ArrayFree(ReverseTradeTickets);
   ReverseOrderCount = 0;
   UseHedgeTakeProfit = false;
   AccumulatedHedgeProfit = 0.0;  // 重置累计利润
   resetTime = TimeCurrent() + InpInitialOrderDelay;
   Print("EA状态已重置。当前时间: ", TimeToString(TimeCurrent()), ", 下次初始建仓时间: ", TimeToString(resetTime));
}

// 添加对冲状态重置函数，确保重置所有相关变量
void ResetHedgeState()
{
   IsHedgeActive = false;
   HedgeStartPrice = 0;
   
   // 重置其他可能影响加仓逻辑的状态
   Print("对冲状态已重置");
}

void AnalyzeTrend()
{
   trendSegments.Clear();
   
   double zigzagPoints[];
   datetime zigzagTimes[];
   int zigzagCount = 0;
   
   // 收集有效的ZigZag点
   for(int i = Bars(_Symbol, PERIOD_CURRENT) - 1; i >= 0; i--)
   {
      if(zigzagBuffer[i] != 0)
      {
         ArrayResize(zigzagPoints, zigzagCount + 1);
         ArrayResize(zigzagTimes, zigzagCount + 1);
         zigzagPoints[zigzagCount] = zigzagBuffer[i];
         zigzagTimes[zigzagCount] = iTime(_Symbol, PERIOD_CURRENT, i);
         zigzagCount++;
      }
   }
   
   // 至少需要6个点来判断趋势（包括延续趋势）
   if(zigzagCount < 6) return;
   
   // 判断趋势
   ENUM_TREND_TYPE prevTrend = TREND_SIDEWAYS;
   
   for(int i = zigzagCount - 1; i >= 3; i--)
   {
      ENUM_TREND_TYPE segmentTrend = DetermineTrend(zigzagPoints[i], zigzagPoints[i-1], zigzagPoints[i-2], zigzagPoints[i-3]);
      
      // 检查是否是延续趋势
      if(i >= 5 && (currentTrend == TREND_UP || currentTrend == TREND_DOWN))
      {
         ENUM_TREND_TYPE nextSegmentTrend = DetermineTrend(zigzagPoints[i-2], zigzagPoints[i-3], zigzagPoints[i-4], zigzagPoints[i-5]);
         if(nextSegmentTrend == currentTrend)
         {
            segmentTrend = currentTrend; // 将回调段标记为与当前趋势相同
         }
      }
      
      // 创建新的趋势段
      CTrendSegment* segment = new CTrendSegment(zigzagTimes[i-1], zigzagTimes[i], zigzagPoints[i-1], zigzagPoints[i], segmentTrend);
      trendSegments.Add(segment);
      
      // 更新趋势
      prevTrend = currentTrend;
      currentTrend = segmentTrend;
   }
   
   // 反向遍历并调整延续趋势的颜色
   ENUM_TREND_TYPE lastDefinitiveTrend = TREND_SIDEWAYS;
   for(int i = 0; i < trendSegments.Total(); i++)
   {
      CTrendSegment* segment = trendSegments.At(i);
      if(segment.trend == TREND_UP || segment.trend == TREND_DOWN)
      {
         lastDefinitiveTrend = segment.trend;
      }
      else if(segment.trend == TREND_SIDEWAYS && lastDefinitiveTrend != TREND_SIDEWAYS)
      {
         // 检查下一个非盘整趋势
         for(int j = i + 1; j < trendSegments.Total(); j++)
         {
            CTrendSegment* nextSegment = trendSegments.At(j);
            if(nextSegment.trend != TREND_SIDEWAYS)
            {
               if(nextSegment.trend == lastDefinitiveTrend)
               {
                  // 将盘整段标记为延续趋势
                  segment.trend = lastDefinitiveTrend;
               }
               break;
            }
         }
      }
   }
   
   // 更新当前趋势
   if(trendSegments.Total() > 0)
   {
      CTrendSegment* latestSegment = trendSegments.At(0);
      currentTrend = latestSegment.trend;
   }
   else
   {
      currentTrend = TREND_SIDEWAYS;
   }

   cachedTrend = currentTrend;
   Print("Current trend: ", EnumToString(cachedTrend));
}

ENUM_TREND_TYPE DetermineTrend(double point1, double point2, double point3, double point4)
{
   bool higherHigh = (point1 > point3) && (point3 > point4);
   bool higherLow = (point2 > point4);
   bool lowerLow = (point1 < point3) && (point3 < point4);
   bool lowerHigh = (point2 < point4);
   
   if(higherHigh && higherLow)
      return TREND_UP;
   else if(lowerLow && lowerHigh)
      return TREND_DOWN;
   else
   {
      // Check if it's within the sideways range
      double range = MathMax(MathMax(point1, point2), MathMax(point3, point4)) - 
                     MathMin(MathMin(point1, point2), MathMin(point3, point4));
      double avgPrice = (point1 + point2 + point3 + point4) / 4;
      if(range / avgPrice <= InpRangePercent)
         return TREND_SIDEWAYS;
      else
         return TREND_SIDEWAYS; // If it's not clearly an uptrend or downtrend, default to sideways
   }
}

void DisplayTrendAreas()
{
   ObjectsDeleteAll(0, "TrendArea_");
   ObjectsDeleteAll(0, "TrendLabel_");
   ObjectsDeleteAll(0, "TrendNumber_");
   
   for(int i = 0; i < trendSegments.Total(); i++)
   {
      CTrendSegment* segment = trendSegments.At(i);
      if(segment == NULL) continue;
      
      string areaName = "TrendArea_" + IntegerToString(i);
      string labelName = "TrendLabel_" + IntegerToString(i);
      string numberNameStart = "TrendNumber_Start_" + IntegerToString(i);
      string numberNameEnd = "TrendNumber_End_" + IntegerToString(i);
      
      // Create trend area
      ObjectCreate(0, areaName, OBJ_RECTANGLE, 0, segment.startTime, segment.startPrice, segment.endTime, segment.endPrice);
      ObjectSetInteger(0, areaName, OBJPROP_COLOR, GetTrendColor(segment.trend));
      ObjectSetInteger(0, areaName, OBJPROP_BACK, true);
      ObjectSetInteger(0, areaName, OBJPROP_FILL, true);
      
      // Create trend label
      ObjectCreate(0, labelName, OBJ_TEXT, 0, (segment.startTime + segment.endTime) / 2, 0);
      ObjectSetString(0, labelName, OBJPROP_TEXT, GetTrendText(segment.trend));
      ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
      ObjectSetDouble(0, labelName, OBJPROP_ANGLE, 0);
      ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LOWER);
      ObjectSetDouble(0, labelName, OBJPROP_PRICE, 0); // Place the label at the bottom of the chart
      
      // Create trend number identifiers (only for uptrends and downtrends)
      if(segment.trend != TREND_SIDEWAYS)
      {
         string startNumber = GetTrendStartNumber(segment.trend);
         string endNumber = GetTrendEndNumber(segment.trend);
         
         ObjectCreate(0, numberNameStart, OBJ_TEXT, 0, segment.startTime, segment.startPrice);
         ObjectSetString(0, numberNameStart, OBJPROP_TEXT, startNumber);
         ObjectSetInteger(0, numberNameStart, OBJPROP_COLOR, clrBlack);
         ObjectSetInteger(0, numberNameStart, OBJPROP_FONTSIZE, 10);
         ObjectSetInteger(0, numberNameStart, OBJPROP_ANCHOR, segment.startPrice < segment.endPrice ? ANCHOR_LOWER : ANCHOR_UPPER);
         
         ObjectCreate(0, numberNameEnd, OBJ_TEXT, 0, segment.endTime, segment.endPrice);
         ObjectSetString(0, numberNameEnd, OBJPROP_TEXT, endNumber);
         ObjectSetInteger(0, numberNameEnd, OBJPROP_COLOR, clrBlack);
         ObjectSetInteger(0, numberNameEnd, OBJPROP_FONTSIZE, 10);
         ObjectSetInteger(0, numberNameEnd, OBJPROP_ANCHOR, segment.startPrice < segment.endPrice ? ANCHOR_UPPER : ANCHOR_LOWER);
      }
   }
}

void DrawZigZag()
{
   ObjectsDeleteAll(0, "ZigZag_");
   
   int counted_bars = Bars(_Symbol, PERIOD_CURRENT);
   double prevZZ = 0;
   datetime prevTime = 0;
   
   for(int i = counted_bars - 1; i >= 0; i--)
   {
      if(zigzagBuffer[i] != 0)
      {
         if(prevZZ != 0)
         {
            string objName = "ZigZag_" + IntegerToString(i);
            ObjectCreate(0, objName, OBJ_TREND, 0, prevTime, prevZZ, iTime(_Symbol, PERIOD_CURRENT, i), zigzagBuffer[i]);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, clrRed);
            ObjectSetInteger(0, objName, OBJPROP_WIDTH, InpZigZagLineWidth);
            ObjectSetInteger(0, objName, OBJPROP_RAY_RIGHT, false);
         }
         prevZZ = zigzagBuffer[i];
         prevTime = iTime(_Symbol, PERIOD_CURRENT, i);
      }
   }
}

color GetTrendColor(ENUM_TREND_TYPE trend)
{
   switch(trend)
   {
      case TREND_UP:
         return InpUpColor;
      case TREND_DOWN:
         return InpDownColor;
      default:
         return InpSidewaysColor;
   }
}

string GetTrendText(ENUM_TREND_TYPE trend)
{
   switch(trend)
   {
      case TREND_UP:
         return "上升趋势";
      case TREND_DOWN:
         return "下跌趋势";
      default:
         return "盘整趋势";
   }
}

string GetTrendStartNumber(ENUM_TREND_TYPE trend)
{
   switch(trend)
   {
      case TREND_UP:
         return "上升";
      case TREND_DOWN:
         return "下降";
      default:
         return "";
   }
}

string GetTrendEndNumber(ENUM_TREND_TYPE trend)
{
   switch(trend)
   {
      case TREND_UP:
         return " ";
      case TREND_DOWN:
         return " ";
      default:
         return "";
   }
}

ENUM_TRADE_DIRECTION GetTradeDirection()
{
   switch(cachedTrend)
   {
      case TREND_UP:
         return InpUpTrendTradeDirection;
      case TREND_DOWN:
         return InpDownTrendTradeDirection;
      case TREND_SIDEWAYS:
         if(InpSidewaysTrendTradeDirection == TRADE_DIRECTION_MFI)
         {
            return GetMFITradeDirection();
         }
         else if(InpSidewaysTrendTradeDirection == TRADE_DIRECTION_PREVIOUS)
         {
            return (previousTrend == TREND_UP) ? TRADE_DIRECTION_BUY : TRADE_DIRECTION_SELL;
         }
         else
         {
            return InpSidewaysTrendTradeDirection;
         }
      default:
         return TRADE_DIRECTION_BOTH;
   }
}

ENUM_TRADE_DIRECTION GetMFITradeDirection()
{
   if(CopyBuffer(mfiHandle, 0, 0, 3, mfiBuffer) != 3)
   {
      Print("复制MFI缓冲区失败");
      return lastMFIDirection;
   }

   double currentMFI = mfiBuffer[0];
   double previousMFI = mfiBuffer[1];
   double olderMFI = mfiBuffer[2];

   if(previousMFI <= InpMFIUpperLevel && currentMFI > InpMFIUpperLevel)
   {
      lastMFIDirection = TRADE_DIRECTION_SELL;
   }
   else if(previousMFI >= InpMFILowerLevel && currentMFI < InpMFILowerLevel)
   {
      lastMFIDirection = TRADE_DIRECTION_BUY;
   }
   else if(currentMFI > InpMFIUpperLevel)
   {
      lastMFIDirection = TRADE_DIRECTION_SELL;
   }
   else if(currentMFI < InpMFILowerLevel)
   {
      lastMFIDirection = TRADE_DIRECTION_BUY;
   }
   else
   {
      // MFI在中间区域，保持上一次的方向
      if(olderMFI > previousMFI && previousMFI > currentMFI)
      {
         lastMFIDirection = TRADE_DIRECTION_SELL; // 下降趋势
      }
      else if(olderMFI < previousMFI && previousMFI < currentMFI)
      {
         lastMFIDirection = TRADE_DIRECTION_BUY; // 上升趋势
      }
      // 如果没有明确趋势，保持上一次的方向
   }

   return lastMFIDirection;
}

string GetErrorDescription(int error_code)
{
   switch(error_code)
   {
      case TRADE_RETCODE_REQUOTE: return "Requote";
      case TRADE_RETCODE_REJECT: return "Request rejected";
      case TRADE_RETCODE_CANCEL: return "Request canceled by trader";
      case TRADE_RETCODE_PLACED: return "Order placed";
      case TRADE_RETCODE_DONE: return "Request completed";
      case TRADE_RETCODE_DONE_PARTIAL: return "Only part of the request was completed";
      case TRADE_RETCODE_ERROR: return "Request processing error";
      case TRADE_RETCODE_TIMEOUT: return "Request canceled by timeout";
      case TRADE_RETCODE_INVALID: return "Invalid request";
      case TRADE_RETCODE_INVALID_VOLUME: return "Invalid volume in the request";
      case TRADE_RETCODE_INVALID_PRICE: return "Invalid price in the request";
      case TRADE_RETCODE_INVALID_STOPS: return "Invalid stops in the request";
      case TRADE_RETCODE_TRADE_DISABLED: return "Trade is disabled";
      case TRADE_RETCODE_MARKET_CLOSED: return "Market is closed";
      case TRADE_RETCODE_NO_MONEY: return "There is not enough money to complete the request";
      case TRADE_RETCODE_PRICE_CHANGED: return "Prices changed";
      case TRADE_RETCODE_PRICE_OFF: return "There are no quotes to process the request";
      case TRADE_RETCODE_INVALID_EXPIRATION: return "Invalid order expiration date in the request";
      case TRADE_RETCODE_ORDER_CHANGED: return "Order state changed";
      case TRADE_RETCODE_TOO_MANY_REQUESTS: return "Too frequent requests";
      case TRADE_RETCODE_NO_CHANGES: return "No changes in request";
      case TRADE_RETCODE_SERVER_DISABLES_AT: return "Autotrading disabled by server";
      case TRADE_RETCODE_CLIENT_DISABLES_AT: return "Autotrading disabled by client terminal";
      case TRADE_RETCODE_LOCKED: return "Request locked for processing";
      case TRADE_RETCODE_FROZEN: return "Order or position frozen";
      case TRADE_RETCODE_INVALID_FILL: return "Invalid order filling type";
      case TRADE_RETCODE_CONNECTION: return "No connection with the trade server";
      case TRADE_RETCODE_ONLY_REAL: return "Operation is allowed only for live accounts";
      case TRADE_RETCODE_LIMIT_ORDERS: return "The number of pending orders has reached the limit";
      case TRADE_RETCODE_LIMIT_VOLUME: return "The volume of orders and positions has reached the limit";
      case ERR_TRADE_SEND_FAILED: return "Trade request sending failed";
      case ERR_TRADE_DISABLED: return "Trade is disabled";
      default: return "Unknown error";
   }
}

string ErrorDescription(int errorCode)
{
   switch(errorCode)
   {
      case 4756: return "Invalid stops (too close to current price)";
      // 添加其他错误代码和描述...
      default: return "Unknown error";
   }
}

void CheckRiskManagement()
{
   if(!HasOpenPositions() || InitialOrderPrice == 0)
      return;

   datetime currentTime = TimeCurrent();
   double totalProfit = CalculateTotalLoss();
   
   // 计算持仓时间
   int holdingHours = CalculateHoldingHours(initialOrderTime, currentTime);
   
   // 计算价格距离
   double currentPrice = (CurrentDirection == TRADE_DIRECTION_BUY) 
                         ? SymbolInfoDouble(_Symbol, SYMBOL_BID) 
                         : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double distance = MathAbs(currentPrice - InitialOrderPrice);
   
   // 修正后的价格距离计算（适用于XAUUSD）
   double distanceUSD = distance * 1 * InpDistanceMultiplier; // 每1美元价格变动等于10点，所以乘以100

   // 每5分钟记录一次日志
   if(currentTime - lastLogTime >= 300) // 300秒 = 5分钟
   {
      Print("风险管理状态：",
            "\n持仓时间：", holdingHours, "小时 (最大：", InpMaxHoldingHours, "小时)",
            "\n当前盈亏：", DoubleToString(totalProfit, 2), "美元",
            "\n最大亏损/盈利限制：", DoubleToString(InpMaxLoss, 2), "美元",
            "\n价格距离：", DoubleToString(distanceUSD, 2), "美元 (最大：", DoubleToString(InpMaxDistanceUSD, 2), "美元)",
            "\n基于距离的最大亏损金额：", DoubleToString(InpDistanceMaxLoss, 2), "美元",
            "\n当前加仓次数：", AdditionCount, " (最大：", InpMaxAdditionCount, ")",
            "\n基于加仓的最大亏损金额：", DoubleToString(InpAdditionMaxLoss, 2), "美元");
      
      lastLogTime = currentTime;
   }

   // 检查持仓时间和最大亏损
   CheckHoldingTimeAndLoss(holdingHours, totalProfit);
   
   // 检查价格距离和最大亏损
   CheckDistanceAndLoss(distanceUSD, totalProfit);
   
   // 检查加仓次数和最大亏损
   CheckAdditionCountAndLoss(totalProfit);
}

void CheckHoldingTimeAndLoss(int holdingHours, double totalProfit)
{
   if(holdingHours >= InpMaxHoldingHours)
   {
      if((InpMaxLoss < 0 && totalProfit > InpMaxLoss) || (InpMaxLoss > 0 && totalProfit > InpMaxLoss))
      {
         Print("达到最大持仓时间和盈亏条件。持仓时间：", holdingHours, "小时，当前盈亏：", DoubleToString(totalProfit, 2), "美元，触发平仓");
         CloseAllPositions();
      }
   }
}

void CheckDistanceAndLoss(double distanceUSD, double totalProfit)
{
   if(distanceUSD >= InpMaxDistanceUSD * InpDistanceMultiplier)
   {
      if((InpDistanceMaxLoss < 0 && totalProfit > InpDistanceMaxLoss) || 
         (InpDistanceMaxLoss > 0 && totalProfit > InpDistanceMaxLoss))
      {
         Print("达到最大价格距离和盈亏条件。价格距离：", DoubleToString(distanceUSD, 2), "美元，当前盈亏：", DoubleToString(totalProfit, 2), "美元，触发平仓");
         CloseAllPositions();
      }
   }
}

void CheckAdditionCountAndLoss(double totalProfit)
{
   if(AdditionCount >= InpMaxAdditionCount)
   {
      if((InpAdditionMaxLoss < 0 && totalProfit > InpAdditionMaxLoss) || 
         (InpAdditionMaxLoss > 0 && totalProfit > InpAdditionMaxLoss))
      {
         Print("达到最大加仓次数和盈亏条件。加仓次数：", AdditionCount, "，当前盈亏：", DoubleToString(totalProfit, 2), "美元，触发平仓");
         CloseAllPositions();
      }
   }
}

int CalculateHoldingHours(datetime startTime, datetime endTime)
{
   int holdingHours = 0;
   for(datetime t = startTime; t <= endTime; t += 3600)
   {
      MqlDateTime dt;
      TimeToStruct(t, dt);
      if(dt.day_of_week > 0 && dt.day_of_week < 6) // 只计算工作日
      {
         if(dt.hour >= 0 && dt.hour < 24) // 只计算交易时间
         {
            holdingHours++;
         }
      }
   }
   return holdingHours;
}